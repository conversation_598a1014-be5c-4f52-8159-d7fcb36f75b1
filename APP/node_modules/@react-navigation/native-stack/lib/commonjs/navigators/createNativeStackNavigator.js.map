{"version": 3, "names": ["NativeStackNavigator", "id", "initialRouteName", "children", "screenListeners", "screenOptions", "rest", "state", "descriptors", "navigation", "NavigationContent", "useNavigationBuilder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "useEffect", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "StackActions", "popToTop", "target", "key", "createNavigatorFactory"], "sourceRoot": "../../../src", "sources": ["navigators/createNativeStackNavigator.tsx"], "mappings": ";;;;;;AAAA;AAWA;AAOA;AAAuD;AAAA;AAAA;AAAA;AAEvD,SAASA,oBAAoB,OAOC;EAAA,IAPA;IAC5BC,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACb,GAAGC;EACsB,CAAC;EAC1B,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzD,IAAAC,4BAAoB,EAMlBC,mBAAW,EAAE;IACbX,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,eAAe;IACfC;EACF,CAAC,CAAC;EAEJQ,KAAK,CAACC,SAAS,CACb;IAAA;IAAA,OACE;MACAL,UAAU,aAAVA,UAAU,gDAAVA,UAAU,CAAEM,WAAW,0DAAvB,2BAAAN,UAAU,EAAgB,UAAU,EAAGO,CAAM,IAAK;QAChD,MAAMC,SAAS,GAAGR,UAAU,CAACQ,SAAS,EAAE;;QAExC;QACA;QACAC,qBAAqB,CAAC,MAAM;UAC1B,IACEX,KAAK,CAACY,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAAgCI,gBAAgB,EACnD;YACA;YACA;YACAX,UAAU,CAACY,QAAQ,CAAC;cAClB,GAAGC,oBAAY,CAACC,QAAQ,EAAE;cAC1BC,MAAM,EAAEjB,KAAK,CAACkB;YAChB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;IAAC;EAAA,GACJ,CAAChB,UAAU,EAAEF,KAAK,CAACY,KAAK,EAAEZ,KAAK,CAACkB,GAAG,CAAC,CACrC;EAED,oBACE,oBAAC,iBAAiB,qBAChB,oBAAC,wBAAe,eACVnB,IAAI;IACR,KAAK,EAAEC,KAAM;IACb,UAAU,EAAEE,UAAW;IACvB,WAAW,EAAED;EAAY,GACzB,CACgB;AAExB;AAAC,eAEc,IAAAkB,8BAAsB,EAKnC1B,oBAAoB,CAAC;AAAA"}