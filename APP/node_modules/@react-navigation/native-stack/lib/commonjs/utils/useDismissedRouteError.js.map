{"version": 3, "names": ["useInvalidPreventRemoveError", "state", "nextDismissedKey", "setNextDismissedKey", "React", "useState", "dismissedRouteName", "routes", "find", "route", "key", "name", "useEffect", "message", "console", "error"], "sourceRoot": "../../../src", "sources": ["utils/useDismissedRouteError.tsx"], "mappings": ";;;;;;AAIA;AAA+B;AAAA;AAEhB,SAASA,4BAA4B,CAClDC,KAA0C,EAC1C;EAAA;EACA,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGC,KAAK,CAACC,QAAQ,CAC5D,IAAI,CACL;EAED,MAAMC,kBAAkB,GAAGJ,gBAAgB,yBACvCD,KAAK,CAACM,MAAM,CAACC,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACC,GAAG,KAAKR,gBAAgB,CAAC,uDAA5D,mBAA8DS,IAAI,GAClE,IAAI;EAERP,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,IAAIN,kBAAkB,EAAE;MACtB,MAAMO,OAAO,GACV,eAAcP,kBAAmB,+DAA8D,GAC/F,6HAA4H,GAC5H,gJAA+I;MAElJQ,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CAACP,kBAAkB,CAAC,CAAC;EAExB,OAAO;IAAEH;EAAoB,CAAC;AAChC"}