"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useInvalidPreventRemoveError;
var _native = require("@react-navigation/native");
var React = _interopRequireWildcard(require("react"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function useInvalidPreventRemoveError(descriptors) {
  var _preventedDescriptor$, _preventedDescriptor$2;
  const {
    preventedRoutes
  } = (0, _native.usePreventRemoveContext)();
  const preventedRouteKey = Object.keys(preventedRoutes)[0];
  const preventedDescriptor = descriptors[preventedRouteKey];
  const isHeaderBackButtonMenuEnabledOnPreventedScreen = preventedDescriptor === null || preventedDescriptor === void 0 ? void 0 : (_preventedDescriptor$ = preventedDescriptor.options) === null || _preventedDescriptor$ === void 0 ? void 0 : _preventedDescriptor$.headerBackButtonMenuEnabled;
  const preventedRouteName = preventedDescriptor === null || preventedDescriptor === void 0 ? void 0 : (_preventedDescriptor$2 = preventedDescriptor.route) === null || _preventedDescriptor$2 === void 0 ? void 0 : _preventedDescriptor$2.name;
  React.useEffect(() => {
    if (preventedRouteKey != null && isHeaderBackButtonMenuEnabledOnPreventedScreen) {
      const message = `The screen ${preventedRouteName} uses 'usePreventRemove' hook alongside 'headerBackButtonMenuEnabled: true', which is not supported. \n\n` + `Consider removing 'headerBackButtonMenuEnabled: true' from ${preventedRouteName} screen to get rid of this error.`;
      console.error(message);
    }
  }, [preventedRouteKey, isHeaderBackButtonMenuEnabledOnPreventedScreen, preventedRouteName]);
}
//# sourceMappingURL=useInvalidPreventRemoveError.js.map