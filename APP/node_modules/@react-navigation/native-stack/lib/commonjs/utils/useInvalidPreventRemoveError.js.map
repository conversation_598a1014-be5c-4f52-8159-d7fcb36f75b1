{"version": 3, "names": ["useInvalidPreventRemoveError", "descriptors", "preventedRoutes", "usePreventRemoveContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "preventedDescriptor", "isHeaderBackButtonMenuEnabledOnPreventedScreen", "options", "headerBackButtonMenuEnabled", "preventedRouteName", "route", "name", "React", "useEffect", "message", "console", "error"], "sourceRoot": "../../../src", "sources": ["utils/useInvalidPreventRemoveError.tsx"], "mappings": ";;;;;;AAAA;AACA;AAA+B;AAAA;AAIhB,SAASA,4BAA4B,CAClDC,WAAqC,EACrC;EAAA;EACA,MAAM;IAAEC;EAAgB,CAAC,GAAG,IAAAC,+BAAuB,GAAE;EACrD,MAAMC,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC,CAAC;EACzD,MAAMK,mBAAmB,GAAGN,WAAW,CAACG,iBAAiB,CAAC;EAC1D,MAAMI,8CAA8C,GAClDD,mBAAmB,aAAnBA,mBAAmB,gDAAnBA,mBAAmB,CAAEE,OAAO,0DAA5B,sBAA8BC,2BAA2B;EAC3D,MAAMC,kBAAkB,GAAGJ,mBAAmB,aAAnBA,mBAAmB,iDAAnBA,mBAAmB,CAAEK,KAAK,2DAA1B,uBAA4BC,IAAI;EAE3DC,KAAK,CAACC,SAAS,CAAC,MAAM;IACpB,IACEX,iBAAiB,IAAI,IAAI,IACzBI,8CAA8C,EAC9C;MACA,MAAMQ,OAAO,GACV,cAAaL,kBAAmB,2GAA0G,GAC1I,8DAA6DA,kBAAmB,mCAAkC;MACrHM,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CACDZ,iBAAiB,EACjBI,8CAA8C,EAC9CG,kBAAkB,CACnB,CAAC;AACJ"}