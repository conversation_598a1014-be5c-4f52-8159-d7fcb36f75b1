{"version": 3, "names": ["TRANSPARENT_PRESENTATIONS", "NativeStackView", "state", "descriptors", "parentHeaderBack", "React", "useContext", "HeaderBackContext", "styles", "container", "routes", "map", "route", "i", "isFocused", "index", "previousKey", "key", "<PERSON><PERSON><PERSON>", "previousDescriptor", "undefined", "nextDescriptor", "options", "navigation", "render", "headerBack", "title", "getHeaderTitle", "name", "canGoBack", "header", "headerShown", "headerTintColor", "headerBackImageSource", "headerLeft", "headerRight", "headerTitle", "headerTitleAlign", "headerTitleStyle", "headerStyle", "headerShadowVisible", "headerTransparent", "headerBackground", "headerBackTitle", "presentation", "contentStyle", "nextPresentation", "back", "tintColor", "label", "backImage", "goBack", "children", "StyleSheet", "absoluteFill", "display", "includes", "backgroundColor", "contentContainer", "create", "flex", "height", "width", "margin", "resizeMode"], "sourceRoot": "../../../src", "sources": ["views/NativeStackView.tsx"], "mappings": ";;;;;;AAAA;AAYA;AACA;AAAuD;AAAA;AAevD,MAAMA,yBAAyB,GAAG,CAChC,kBAAkB,EAClB,2BAA2B,CAC5B;AAEc,SAASC,eAAe,OAAgC;EAAA,IAA/B;IAAEC,KAAK;IAAEC;EAAmB,CAAC;EACnE,MAAMC,gBAAgB,GAAGC,KAAK,CAACC,UAAU,CAACC,2BAAiB,CAAC;EAE5D,oBACE,oBAAC,gCAAsB,qBACrB,oBAAC,iBAAI;IAAC,KAAK,EAAEC,MAAM,CAACC;EAAU,GAC3BP,KAAK,CAACQ,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;IAAA;IAC9B,MAAMC,SAAS,GAAGZ,KAAK,CAACa,KAAK,KAAKF,CAAC;IACnC,MAAMG,WAAW,oBAAGd,KAAK,CAACQ,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,kDAAnB,cAAqBI,GAAG;IAC5C,MAAMC,OAAO,qBAAGhB,KAAK,CAACQ,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,mDAAnB,eAAqBI,GAAG;IACxC,MAAME,kBAAkB,GAAGH,WAAW,GAClCb,WAAW,CAACa,WAAW,CAAC,GACxBI,SAAS;IACb,MAAMC,cAAc,GAAGH,OAAO,GAAGf,WAAW,CAACe,OAAO,CAAC,GAAGE,SAAS;IACjE,MAAM;MAAEE,OAAO;MAAEC,UAAU;MAAEC;IAAO,CAAC,GAAGrB,WAAW,CAACS,KAAK,CAACK,GAAG,CAAC;IAE9D,MAAMQ,UAAU,GAAGN,kBAAkB,GACjC;MACEO,KAAK,EAAE,IAAAC,wBAAc,EACnBR,kBAAkB,CAACG,OAAO,EAC1BH,kBAAkB,CAACP,KAAK,CAACgB,IAAI;IAEjC,CAAC,GACDxB,gBAAgB;IAEpB,MAAMyB,SAAS,GAAGJ,UAAU,KAAKL,SAAS;IAE1C,MAAM;MACJU,MAAM;MACNC,WAAW;MACXC,eAAe;MACfC,qBAAqB;MACrBC,UAAU;MACVC,WAAW;MACXC,WAAW;MACXC,gBAAgB;MAChBC,gBAAgB;MAChBC,WAAW;MACXC,mBAAmB;MACnBC,iBAAiB;MACjBC,gBAAgB;MAChBC,eAAe;MACfC,YAAY;MACZC;IACF,CAAC,GAAGvB,OAAO;IAEX,MAAMwB,gBAAgB,GAAGzB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEC,OAAO,CAACsB,YAAY;IAE7D,oBACE,oBAAC,gBAAM;MACL,GAAG,EAAEhC,KAAK,CAACK,GAAI;MACf,OAAO,EAAEH,SAAU;MACnB,KAAK,EAAEF,KAAM;MACb,UAAU,EAAEW,UAAW;MACvB,WAAW,EAAEQ,WAAY;MACzB,iBAAiB,EAAEU,iBAAkB;MACrC,MAAM,EACJX,MAAM,KAAKV,SAAS,GAClBU,MAAM,CAAC;QACLiB,IAAI,EAAEtB,UAAU;QAChBH,OAAO;QACPV,KAAK;QACLW;MACF,CAAC,CAAC,gBAEF,oBAAC,gBAAM;QACL,KAAK,EAAE,IAAAI,wBAAc,EAACL,OAAO,EAAEV,KAAK,CAACgB,IAAI,CAAE;QAC3C,eAAe,EAAEI,eAAgB;QACjC,UAAU,EACR,OAAOE,UAAU,KAAK,UAAU,GAC5B;UAAA,IAAC;YAAEc;UAAU,CAAC;UAAA,OACZd,UAAU,CAAC;YACTc,SAAS;YACTnB,SAAS;YACToB,KAAK,EAAEN;UACT,CAAC,CAAC;QAAA,IACJT,UAAU,KAAKd,SAAS,IAAIS,SAAS,GACrC;UAAA,IAAC;YAAEmB;UAAU,CAAC;UAAA,oBACZ,oBAAC,0BAAgB;YACf,SAAS,EAAEA,SAAU;YACrB,SAAS,EACPf,qBAAqB,KAAKb,SAAS,GAC/B,mBACE,oBAAC,kBAAK;cACJ,MAAM,EAAEa,qBAAsB;cAC9B,KAAK,EAAE,CACLzB,MAAM,CAAC0C,SAAS,EAChB;gBAAEF;cAAU,CAAC;YACb,EAEL,GACD5B,SACL;YACD,OAAO,EAAEG,UAAU,CAAC4B,MAAO;YAC3B,SAAS,EAAEtB;UAAU,EACrB;QAAA,CACH,GACDK,UACL;QACD,WAAW,EACT,OAAOC,WAAW,KAAK,UAAU,GAC7B;UAAA,IAAC;YAAEa;UAAU,CAAC;UAAA,OACZb,WAAW,CAAC;YAAEa,SAAS;YAAEnB;UAAU,CAAC,CAAC;QAAA,IACvCM,WACL;QACD,WAAW,EACT,OAAOC,WAAW,KAAK,UAAU,GAC7B;UAAA,IAAC;YAAEgB,QAAQ;YAAEJ;UAAU,CAAC;UAAA,OACtBZ,WAAW,CAAC;YAAEgB,QAAQ;YAAEJ;UAAU,CAAC,CAAC;QAAA,IACtCZ,WACL;QACD,gBAAgB,EAAEC,gBAAiB;QACnC,gBAAgB,EAAEC,gBAAiB;QACnC,iBAAiB,EAAEG,iBAAkB;QACrC,mBAAmB,EAAED,mBAAoB;QACzC,gBAAgB,EAAEE,gBAAiB;QACnC,WAAW,EAAEH;MAAY,EAG9B;MACD,KAAK,EAAE,CACLc,uBAAU,CAACC,YAAY,EACvB;QACEC,OAAO,EACLzC,SAAS,IACRgC,gBAAgB,IAAI,IAAI,IACvB9C,yBAAyB,CAACwD,QAAQ,CAACV,gBAAgB,CAAE,GACnD,MAAM,GACN;MACR,CAAC,EACDF,YAAY,IAAI,IAAI,IACpB5C,yBAAyB,CAACwD,QAAQ,CAACZ,YAAY,CAAC,GAC5C;QAAEa,eAAe,EAAE;MAAc,CAAC,GAClC,IAAI;IACR,gBAEF,oBAAC,2BAAiB,CAAC,QAAQ;MAAC,KAAK,EAAEhC;IAAW,gBAC5C,oBAAC,iBAAI;MAAC,KAAK,EAAE,CAACjB,MAAM,CAACkD,gBAAgB,EAAEb,YAAY;IAAE,GAClDrB,MAAM,EAAE,CACJ,CACoB,CACtB;EAEb,CAAC,CAAC,CACG,CACgB;AAE7B;AAEA,MAAMhB,MAAM,GAAG6C,uBAAU,CAACM,MAAM,CAAC;EAC/BlD,SAAS,EAAE;IACTmD,IAAI,EAAE;EACR,CAAC;EACDF,gBAAgB,EAAE;IAChBE,IAAI,EAAE;EACR,CAAC;EACDV,SAAS,EAAE;IACTW,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;EACd;AACF,CAAC,CAAC"}