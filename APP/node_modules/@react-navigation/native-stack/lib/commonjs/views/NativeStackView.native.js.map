{"version": 3, "names": ["isAndroid", "Platform", "OS", "MaybeNestedStack", "options", "route", "presentation", "headerHeight", "headerTopInsetEnabled", "children", "colors", "useTheme", "header", "headerShown", "contentStyle", "isHeaderInModal", "undefined", "headerShownPreviousRef", "React", "useRef", "useEffect", "warnOnce", "current", "name", "content", "styles", "container", "backgroundColor", "background", "headerLargeTitle", "StyleSheet", "absoluteFill", "SceneView", "index", "focused", "descriptor", "previousDescriptor", "nextDescriptor", "onWillDisappear", "onWillAppear", "onAppear", "onDisappear", "onDismissed", "onHeaderBackButtonClicked", "onNativeDismissCancelled", "onGestureCancel", "navigation", "render", "animationDuration", "animationTypeForReplace", "fullScreenGestureShadowEnabled", "gestureEnabled", "gestureResponseDistance", "headerBackButtonMenuEnabled", "headerBackground", "headerTransparent", "autoHideHomeIndicator", "keyboardHandlingEnabled", "navigationBarColor", "navigationBarHidden", "orientation", "sheetAllowedDetents", "sheetLargestUndimmedDetent", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "statusBarAnimation", "statusBarHidden", "statusBarStyle", "statusBarTranslucent", "statusBarColor", "freezeOnBlur", "animation", "customAnimationOnGesture", "fullScreenGestureEnabled", "gestureDirection", "nextGestureDirection", "gestureDirectionOverride", "insets", "useSafeAreaInsets", "frame", "useSafeAreaFrame", "isModal", "isIPhone", "isPad", "isTV", "isLandscape", "width", "height", "isParentHeaderShown", "useContext", "HeaderShownContext", "parentHeaderHeight", "HeaderHeightContext", "parentHeaderBack", "HeaderBackContext", "topInset", "top", "hasDynamicIsland", "statusBarHeight", "preventedRoutes", "usePreventRemoveContext", "defaultHeaderHeight", "getDefaultHeaderHeight", "customHeaderHeight", "setCustomHeaderHeight", "useState", "headerBack", "title", "getHeaderTitle", "isRemovePrevented", "key", "preventRemove", "translucent", "scene", "e", "nativeEvent", "layout", "absolute", "back", "headerBackTitle", "NativeStackViewInner", "state", "descriptors", "setNextDismissedKey", "useDismissedRouteError", "useInvalidPreventRemoveError", "routes", "map", "isFocused", "previousKey", "<PERSON><PERSON><PERSON>", "emit", "type", "data", "closing", "target", "event", "dispatch", "StackActions", "pop", "dismissCount", "source", "NativeStackView", "props", "create", "flex", "flexDirection", "position", "left", "right", "zIndex", "elevation", "overflow"], "sourceRoot": "../../../src", "sources": ["views/NativeStackView.native.tsx"], "mappings": ";;;;;;AAAA;AAQA;AAUA;AACA;AACA;AAKA;AAKA;AAQA;AACA;AACA;AACA;AAA0C;AAAA;AAAA;AAAA;AAE1C,MAAMA,SAAS,GAAGC,qBAAQ,CAACC,EAAE,KAAK,SAAS;AAE3C,MAAMC,gBAAgB,GAAG,QAcnB;EAAA,IAdoB;IACxBC,OAAO;IACPC,KAAK;IACLC,YAAY;IACZC,YAAY;IACZC,qBAAqB;IACrBC;EAQF,CAAC;EACC,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,gBAAQ,GAAE;EAC7B,MAAM;IAAEC,MAAM;IAAEC,WAAW,GAAG,IAAI;IAAEC;EAAa,CAAC,GAAGV,OAAO;EAE5D,MAAMW,eAAe,GAAGf,SAAS,GAC7B,KAAK,GACLM,YAAY,KAAK,MAAM,IAAIO,WAAW,KAAK,IAAI,IAAID,MAAM,KAAKI,SAAS;EAE3E,MAAMC,sBAAsB,GAAGC,KAAK,CAACC,MAAM,CAACN,WAAW,CAAC;EAExDK,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,IAAAC,iBAAQ,EACN,CAACrB,SAAS,IACRM,YAAY,KAAK,MAAM,IACvBW,sBAAsB,CAACK,OAAO,KAAKT,WAAW,EAC/C,6IAA4IR,KAAK,CAACkB,IAAK,IAAG,CAC5J;IAEDN,sBAAsB,CAACK,OAAO,GAAGT,WAAW;EAC9C,CAAC,EAAE,CAACA,WAAW,EAAEP,YAAY,EAAED,KAAK,CAACkB,IAAI,CAAC,CAAC;EAE3C,MAAMC,OAAO,gBACX,oBAAC,uBAAc;IACb,KAAK,EAAE,CACLC,MAAM,CAACC,SAAS,EAChBpB,YAAY,KAAK,kBAAkB,IACjCA,YAAY,KAAK,2BAA2B,IAAI;MAC9CqB,eAAe,EAAEjB,MAAM,CAACkB;IAC1B,CAAC,EACHd,YAAY,CACZ;IACF,iBAAiB,EAAER,YAAY,KAAK,MAAM,GAAG,MAAM,GAAGA;EAAa,GAElEG,QAAQ,CAEZ;EAED,IAAIM,eAAe,EAAE;IACnB,oBACE,oBAAC,+BAAW;MAAC,KAAK,EAAEU,MAAM,CAACC;IAAU,gBACnC,oBAAC,0BAAM;MACL,OAAO;MACP,aAAa;MACb,cAAc,EAAEtB,OAAO,CAACyB,gBAAgB,IAAI,KAAM;MAClD,KAAK,EAAEC,uBAAU,CAACC;IAAa,GAE9BP,OAAO,eACR,oBAAC,qBAAY,eACPpB,OAAO;MACX,KAAK,EAAEC,KAAM;MACb,YAAY,EAAEE,YAAa;MAC3B,qBAAqB,EAAEC,qBAAsB;MAC7C,SAAS;IAAA,GACT,CACK,CACG;EAElB;EAEA,OAAOgB,OAAO;AAChB,CAAC;AAkBD,MAAMQ,SAAS,GAAG,SAcI;EAAA;EAAA,IAdH;IACjBC,KAAK;IACLC,OAAO;IACPC,UAAU;IACVC,kBAAkB;IAClBC,cAAc;IACdC,eAAe;IACfC,YAAY;IACZC,QAAQ;IACRC,WAAW;IACXC,WAAW;IACXC,yBAAyB;IACzBC,wBAAwB;IACxBC;EACc,CAAC;EACf,MAAM;IAAExC,KAAK;IAAEyC,UAAU;IAAE1C,OAAO;IAAE2C;EAAO,CAAC,GAAGZ,UAAU;EACzD,MAAM;IACJa,iBAAiB;IACjBC,uBAAuB,GAAG,MAAM;IAChCC,8BAA8B,GAAG,KAAK;IACtCC,cAAc;IACdC,uBAAuB;IACvBxC,MAAM;IACNyC,2BAA2B;IAC3BxC,WAAW;IACXyC,gBAAgB;IAChBC,iBAAiB;IACjBC,qBAAqB;IACrBC,uBAAuB;IACvBC,kBAAkB;IAClBC,mBAAmB;IACnBC,WAAW;IACXC,mBAAmB,GAAG,OAAO;IAC7BC,0BAA0B,GAAG,KAAK;IAClCC,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,kBAAkB;IAClBC,eAAe;IACfC,cAAc;IACdC,oBAAoB;IACpBC,cAAc;IACdC;EACF,CAAC,GAAGnE,OAAO;EAEX,IAAI;IACFoE,SAAS;IACTC,wBAAwB;IACxBC,wBAAwB;IACxBpE,YAAY,GAAG,MAAM;IACrBqE,gBAAgB,GAAGrE,YAAY,KAAK,MAAM,GAAG,YAAY,GAAG;EAC9D,CAAC,GAAGF,OAAO;EAEX,IAAIuE,gBAAgB,KAAK,UAAU,IAAI1E,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IAC5D;IACA;IACA;IACA;IACA;IACA,IAAIwE,wBAAwB,KAAK1D,SAAS,EAAE;MAC1C0D,wBAAwB,GAAG,IAAI;IACjC;IACA,IAAID,wBAAwB,KAAKzD,SAAS,EAAE;MAC1CyD,wBAAwB,GAAG,IAAI;IACjC;IACA,IAAID,SAAS,KAAKxD,SAAS,EAAE;MAC3BwD,SAAS,GAAG,mBAAmB;IACjC;EACF;;EAEA;EACA;EACA,MAAMI,oBAAoB,GAAGvC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjC,OAAO,CAACuE,gBAAgB;EACrE,MAAME,wBAAwB,GAC5BD,oBAAoB,IAAI,IAAI,GAAGA,oBAAoB,GAAGD,gBAAgB;EAExE,IAAI1C,KAAK,KAAK,CAAC,EAAE;IACf;IACA;IACA3B,YAAY,GAAG,MAAM;EACvB;EAEA,MAAMwE,MAAM,GAAG,IAAAC,6CAAiB,GAAE;EAClC,MAAMC,KAAK,GAAG,IAAAC,4CAAgB,GAAE;;EAEhC;EACA,MAAMC,OAAO,GAAG5E,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,WAAW;;EAExE;EACA,MAAM6E,QAAQ,GAAGlF,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI,EAAED,qBAAQ,CAACmF,KAAK,IAAInF,qBAAQ,CAACoF,IAAI,CAAC;EAC5E,MAAMC,WAAW,GAAGN,KAAK,CAACO,KAAK,GAAGP,KAAK,CAACQ,MAAM;EAE9C,MAAMC,mBAAmB,GAAGvE,KAAK,CAACwE,UAAU,CAACC,4BAAkB,CAAC;EAChE,MAAMC,kBAAkB,GAAG1E,KAAK,CAACwE,UAAU,CAACG,6BAAmB,CAAC;EAChE,MAAMC,gBAAgB,GAAG5E,KAAK,CAACwE,UAAU,CAACK,2BAAiB,CAAC;EAE5D,MAAMC,QAAQ,GACZP,mBAAmB,IAClBxF,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAIgF,OAAQ,IACjCC,QAAQ,IAAIG,WAAY,GACrB,CAAC,GACDR,MAAM,CAACmB,GAAG;;EAEhB;EACA,MAAMC,gBAAgB,GAAGjG,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI8F,QAAQ,GAAG,EAAE;EAC/D,MAAMG,eAAe,GAAGD,gBAAgB,GAAGF,QAAQ,GAAG,CAAC,GAAGA,QAAQ;EAElE,MAAM;IAAEI;EAAgB,CAAC,GAAG,IAAAC,+BAAuB,GAAE;EAErD,MAAMC,mBAAmB,GAAG,IAAAC,gCAAsB,EAChDvB,KAAK,EACLE,OAAO,EACPiB,eAAe,CAChB;EAED,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAC/CvF,KAAK,CAACwF,QAAQ,CAACJ,mBAAmB,CAAC;;EAErC;EACA;EACA;EACA;EACA;EACA;EACA,MAAM9F,qBAAqB,GACzB,OAAO6D,oBAAoB,KAAK,SAAS,GACrCA,oBAAoB,GACpB2B,QAAQ,KAAK,CAAC;EAEpB,MAAMzF,YAAY,GAAGK,MAAM,GAAG4F,kBAAkB,GAAGF,mBAAmB;EACtE,MAAMK,UAAU,GAAGvE,kBAAkB,GACjC;IACEwE,KAAK,EAAE,IAAAC,wBAAc,EACnBzE,kBAAkB,CAAChC,OAAO,EAC1BgC,kBAAkB,CAAC/B,KAAK,CAACkB,IAAI;EAEjC,CAAC,GACDuE,gBAAgB;EAEpB,MAAMgB,iBAAiB,4BAAGV,eAAe,CAAC/F,KAAK,CAAC0G,GAAG,CAAC,0DAA1B,sBAA4BC,aAAa;EAEnE,oBACE,oBAAC,0BAAM;IACL,GAAG,EAAE3G,KAAK,CAAC0G,GAAI;IACf,OAAO;IACP,aAAa;IACb,KAAK,EAAEjF,uBAAU,CAACC,YAAa;IAC/B,cAAc,EAAE3B,OAAO,CAACyB,gBAAgB,IAAI,KAAM;IAClD,sBAAsB,EAAE4C,wBAAyB;IACjD,sBAAsB,EAAEC;IACxB;IAAA;IACA,4BAA4B,EAAExB,8BAA+B;IAC7D,cAAc,EACZlD,SAAS;IACL;IACA;IACA,KAAK,GACLmD,cACL;IACD,mBAAmB,EAAEK,qBAAsB;IAC3C,mBAAmB,EAAEC,uBAAwB;IAC7C,kBAAkB,EAAEC,kBAAmB;IACvC,mBAAmB,EAAEC,mBAAoB;IACzC,gBAAgB,EAAEV,uBAAwB;IAC1C,iBAAiB,EAAE3C,YAAY,KAAK,MAAM,GAAG,MAAM,GAAGA,YAAa;IACnE,cAAc,EAAEkE,SAAU;IAC1B,iBAAiB,EAAEZ,WAAY;IAC/B,mBAAmB,EAAEC,mBAAoB;IACzC,0BAA0B,EAAEC,0BAA2B;IACvD,mBAAmB,EAAEC,mBAAoB;IACzC,iBAAiB,EAAEC,iBAAkB;IACrC,8BAA8B,EAAEC,8BAA+B;IAC/D,kBAAkB,EAAEC,kBAAmB;IACvC,eAAe,EAAEC,eAAgB;IACjC,cAAc,EAAEC,cAAe;IAC/B,cAAc,EAAEE,cAAe;IAC/B,oBAAoB,EAAED,oBAAqB;IAC3C,cAAc,EAAEQ,wBAAyB;IACzC,kBAAkB,EAAE7B,iBAAkB;IACtC,eAAe,EAAEV,eAAgB;IACjC,YAAY,EAAEC,YAAa;IAC3B,QAAQ,EAAEC,QAAS;IACnB,WAAW,EAAEC,WAAY;IACzB,WAAW,EAAEC,WAAY;IACzB,eAAe,EAAEG,eAAgB;IACjC,uBAAuB,EAAEO,uBAAwB;IACjD,gCAAgC,EAAE,KAAM,CAAC;IAAA;IACzC,yBAAyB,EAAET;IAC3B;IAAA;IACA,oBAAoB,EAAEmE,iBAAkB,CAAC;IAAA;IACzC,wBAAwB,EAAElE;IAC1B;IAAA;IACA,YAAY,EAAE2B;EAAa,gBAE3B,oBAAC,yBAAiB,CAAC,QAAQ;IAAC,KAAK,EAAEzB;EAAW,gBAC5C,oBAAC,8BAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEzC;EAAM,gBAC5C,oBAAC,4BAAkB,CAAC,QAAQ;IAC1B,KAAK,EAAEoF,mBAAmB,IAAI5E,WAAW,KAAK;EAAM,gBAEpD,oBAAC,6BAAmB,CAAC,QAAQ;IAC3B,KAAK,EACHA,WAAW,KAAK,KAAK,GAAGN,YAAY,GAAGqF,kBAAkB,IAAI;EAC9D,GAEAtC,gBAAgB,IAAI,IAAI;EAAA;EACvB;AAChB;AACA;AACA;EACgB,oBAAC,iBAAI;IACH,KAAK,EAAE,CACL7B,MAAM,CAACG,UAAU,EACjB2B,iBAAiB,GAAG9B,MAAM,CAACwF,WAAW,GAAG,IAAI,EAC7C;MAAEzB,MAAM,EAAEjF;IAAa,CAAC;EACxB,GAED+C,gBAAgB,EAAE,CACd,GACL,IAAI,eACR,oBAAC,iBAAI;IACH,2BAA2B,EAAE,CAACpB,OAAQ;IACtC,yBAAyB,EACvBA,OAAO,GAAG,MAAM,GAAG,qBACpB;IACD,KAAK,EAAET,MAAM,CAACyF;EAAM,gBAEpB,oBAAC,gBAAgB;IACf,OAAO,EAAE9G,OAAQ;IACjB,KAAK,EAAEC,KAAM;IACb,YAAY,EAAEC,YAAa;IAC3B,YAAY,EAAEC,YAAa;IAC3B,qBAAqB,EAAEC;EAAsB,gBAE7C,oBAAC,2BAAiB,CAAC,QAAQ;IAAC,KAAK,EAAEmG;EAAW,GAC3C5D,MAAM,EAAE,CACkB,CACZ,EAClBnC,MAAM,KAAKI,SAAS,IAAIH,WAAW,KAAK,KAAK,gBAC5C,oBAAC,iBAAI;IACH,QAAQ,EAAGsG,CAAC,IAAK;MACfV,qBAAqB,CAACU,CAAC,CAACC,WAAW,CAACC,MAAM,CAAC7B,MAAM,CAAC;IACpD,CAAE;IACF,KAAK,EAAEjC,iBAAiB,GAAG9B,MAAM,CAAC6F,QAAQ,GAAG;EAAK,GAEjD1G,MAAM,CAAC;IACN2G,IAAI,EAAEZ,UAAU;IAChBvG,OAAO;IACPC,KAAK;IACLyC;EACF,CAAC,CAAC,CACG,GACL,IAAI,CACH,eAYP,oBAAC,qBAAY,eACP1C,OAAO;IACX,KAAK,EAAEC,KAAM;IACb,2BAA2B,EACzByG,iBAAiB,KAAK9F,SAAS,GAC3B,CAAC8F,iBAAiB,GAClBzD,2BACL;IACD,WAAW,EAAEzC,MAAM,KAAKI,SAAS,GAAG,KAAK,GAAGH,WAAY;IACxD,YAAY,EAAEN,YAAa;IAC3B,eAAe,EACbH,OAAO,CAACoH,eAAe,KAAKxG,SAAS,GACjCZ,OAAO,CAACoH,eAAe,GACvBxG,SACL;IACD,qBAAqB,EAAER,qBAAsB;IAC7C,SAAS,EAAEmG,UAAU,KAAK3F;EAAU,GACpC,CAC2B,CACH,CACE,CACP,CACtB;AAEb,CAAC;AAQD,SAASyG,oBAAoB,QAA4C;EAAA,IAA3C;IAAEC,KAAK;IAAE5E,UAAU;IAAE6E;EAAmB,CAAC;EACrE,MAAM;IAAEC;EAAoB,CAAC,GAAG,IAAAC,+BAAsB,EAACH,KAAK,CAAC;EAE7D,IAAAI,qCAA4B,EAACH,WAAW,CAAC;EAEzC,oBACE,oBAAC,+BAAW;IAAC,KAAK,EAAElG,MAAM,CAACC;EAAU,GAClCgG,KAAK,CAACK,MAAM,CAACC,GAAG,CAAC,CAAC3H,KAAK,EAAE4B,KAAK,KAAK;IAAA;IAClC,MAAME,UAAU,GAAGwF,WAAW,CAACtH,KAAK,CAAC0G,GAAG,CAAC;IACzC,MAAMkB,SAAS,GAAGP,KAAK,CAACzF,KAAK,KAAKA,KAAK;IACvC,MAAMiG,WAAW,oBAAGR,KAAK,CAACK,MAAM,CAAC9F,KAAK,GAAG,CAAC,CAAC,kDAAvB,cAAyB8E,GAAG;IAChD,MAAMoB,OAAO,qBAAGT,KAAK,CAACK,MAAM,CAAC9F,KAAK,GAAG,CAAC,CAAC,mDAAvB,eAAyB8E,GAAG;IAC5C,MAAM3E,kBAAkB,GAAG8F,WAAW,GAClCP,WAAW,CAACO,WAAW,CAAC,GACxBlH,SAAS;IACb,MAAMqB,cAAc,GAAG8F,OAAO,GAAGR,WAAW,CAACQ,OAAO,CAAC,GAAGnH,SAAS;IAEjE,oBACE,oBAAC,SAAS;MACR,GAAG,EAAEX,KAAK,CAAC0G,GAAI;MACf,KAAK,EAAE9E,KAAM;MACb,OAAO,EAAEgG,SAAU;MACnB,UAAU,EAAE9F,UAAW;MACvB,kBAAkB,EAAEC,kBAAmB;MACvC,cAAc,EAAEC,cAAe;MAC/B,eAAe,EAAE,MAAM;QACrBS,UAAU,CAACsF,IAAI,CAAC;UACdC,IAAI,EAAE,iBAAiB;UACvBC,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAK,CAAC;UACvBC,MAAM,EAAEnI,KAAK,CAAC0G;QAChB,CAAC,CAAC;MACJ,CAAE;MACF,YAAY,EAAE,MAAM;QAClBjE,UAAU,CAACsF,IAAI,CAAC;UACdC,IAAI,EAAE,iBAAiB;UACvBC,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAM,CAAC;UACxBC,MAAM,EAAEnI,KAAK,CAAC0G;QAChB,CAAC,CAAC;MACJ,CAAE;MACF,QAAQ,EAAE,MAAM;QACdjE,UAAU,CAACsF,IAAI,CAAC;UACdC,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAM,CAAC;UACxBC,MAAM,EAAEnI,KAAK,CAAC0G;QAChB,CAAC,CAAC;MACJ,CAAE;MACF,WAAW,EAAE,MAAM;QACjBjE,UAAU,CAACsF,IAAI,CAAC;UACdC,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE;YAAEC,OAAO,EAAE;UAAK,CAAC;UACvBC,MAAM,EAAEnI,KAAK,CAAC0G;QAChB,CAAC,CAAC;MACJ,CAAE;MACF,WAAW,EAAG0B,KAAK,IAAK;QACtB3F,UAAU,CAAC4F,QAAQ,CAAC;UAClB,GAAGC,oBAAY,CAACC,GAAG,CAACH,KAAK,CAACrB,WAAW,CAACyB,YAAY,CAAC;UACnDC,MAAM,EAAEzI,KAAK,CAAC0G,GAAG;UACjByB,MAAM,EAAEd,KAAK,CAACX;QAChB,CAAC,CAAC;QAEFa,mBAAmB,CAACvH,KAAK,CAAC0G,GAAG,CAAC;MAChC,CAAE;MACF,yBAAyB,EAAE,MAAM;QAC/BjE,UAAU,CAAC4F,QAAQ,CAAC;UAClB,GAAGC,oBAAY,CAACC,GAAG,EAAE;UACrBE,MAAM,EAAEzI,KAAK,CAAC0G,GAAG;UACjByB,MAAM,EAAEd,KAAK,CAACX;QAChB,CAAC,CAAC;MACJ,CAAE;MACF,wBAAwB,EAAG0B,KAAK,IAAK;QACnC3F,UAAU,CAAC4F,QAAQ,CAAC;UAClB,GAAGC,oBAAY,CAACC,GAAG,CAACH,KAAK,CAACrB,WAAW,CAACyB,YAAY,CAAC;UACnDC,MAAM,EAAEzI,KAAK,CAAC0G,GAAG;UACjByB,MAAM,EAAEd,KAAK,CAACX;QAChB,CAAC,CAAC;MACJ,CAAE;MACF,eAAe,EAAE,MAAM;QACrBjE,UAAU,CAACsF,IAAI,CAAC;UACdC,IAAI,EAAE,eAAe;UACrBG,MAAM,EAAEnI,KAAK,CAAC0G;QAChB,CAAC,CAAC;MACJ;IAAE,EACF;EAEN,CAAC,CAAC,CACU;AAElB;AAEe,SAASgC,eAAe,CAACC,KAAY,EAAE;EACpD,oBACE,oBAAC,gCAAsB,qBACrB,oBAAC,oBAAoB,EAAKA,KAAK,CAAI,CACZ;AAE7B;AAEA,MAAMvH,MAAM,GAAGK,uBAAU,CAACmH,MAAM,CAAC;EAC/BvH,SAAS,EAAE;IACTwH,IAAI,EAAE;EACR,CAAC;EACDhC,KAAK,EAAE;IACLgC,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB,CAAC;EACD7B,QAAQ,EAAE;IACR8B,QAAQ,EAAE,UAAU;IACpBnD,GAAG,EAAE,CAAC;IACNoD,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC;EACDrC,WAAW,EAAE;IACXmC,QAAQ,EAAE,UAAU;IACpBnD,GAAG,EAAE,CAAC;IACNoD,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE;EACb,CAAC;EACD5H,UAAU,EAAE;IACV6H,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC"}