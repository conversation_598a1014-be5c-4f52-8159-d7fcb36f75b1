{"version": 3, "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "NativeStackView", "NativeStackNavigator", "id", "initialRouteName", "children", "screenListeners", "screenOptions", "rest", "state", "descriptors", "navigation", "NavigationContent", "useEffect", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "popToTop", "target", "key"], "sourceRoot": "../../../src", "sources": ["navigators/createNativeStackNavigator.tsx"], "mappings": ";AAAA,SACEA,sBAAsB,EAItBC,YAAY,EAEZC,WAAW,EAEXC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAO9B,OAAOC,eAAe,MAAM,0BAA0B;AAEtD,SAASC,oBAAoB,OAOC;EAAA,IAPA;IAC5BC,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,eAAe;IACfC,aAAa;IACb,GAAGC;EACsB,CAAC;EAC1B,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzDb,oBAAoB,CAMlBD,WAAW,EAAE;IACbK,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,eAAe;IACfC;EACF,CAAC,CAAC;EAEJP,KAAK,CAACa,SAAS,CACb;IAAA;IAAA,OACE;MACAF,UAAU,aAAVA,UAAU,gDAAVA,UAAU,CAAEG,WAAW,0DAAvB,2BAAAH,UAAU,EAAgB,UAAU,EAAGI,CAAM,IAAK;QAChD,MAAMC,SAAS,GAAGL,UAAU,CAACK,SAAS,EAAE;;QAExC;QACA;QACAC,qBAAqB,CAAC,MAAM;UAC1B,IACER,KAAK,CAACS,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAAgCI,gBAAgB,EACnD;YACA;YACA;YACAR,UAAU,CAACS,QAAQ,CAAC;cAClB,GAAGvB,YAAY,CAACwB,QAAQ,EAAE;cAC1BC,MAAM,EAAEb,KAAK,CAACc;YAChB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;IAAC;EAAA,GACJ,CAACZ,UAAU,EAAEF,KAAK,CAACS,KAAK,EAAET,KAAK,CAACc,GAAG,CAAC,CACrC;EAED,oBACE,oBAAC,iBAAiB,qBAChB,oBAAC,eAAe,eACVf,IAAI;IACR,KAAK,EAAEC,KAAM;IACb,UAAU,EAAEE,UAAW;IACvB,WAAW,EAAED;EAAY,GACzB,CACgB;AAExB;AAEA,eAAed,sBAAsB,CAKnCM,oBAAoB,CAAC"}