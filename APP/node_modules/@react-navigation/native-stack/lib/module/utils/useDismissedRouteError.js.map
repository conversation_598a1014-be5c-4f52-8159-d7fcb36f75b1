{"version": 3, "names": ["React", "useInvalidPreventRemoveError", "state", "nextDismissedKey", "setNextDismissedKey", "useState", "dismissedRouteName", "routes", "find", "route", "key", "name", "useEffect", "message", "console", "error"], "sourceRoot": "../../../src", "sources": ["utils/useDismissedRouteError.tsx"], "mappings": "AAIA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,eAAe,SAASC,4BAA4B,CAClDC,KAA0C,EAC1C;EAAA;EACA,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGJ,KAAK,CAACK,QAAQ,CAC5D,IAAI,CACL;EAED,MAAMC,kBAAkB,GAAGH,gBAAgB,yBACvCD,KAAK,CAACK,MAAM,CAACC,IAAI,CAAEC,KAAK,IAAKA,KAAK,CAACC,GAAG,KAAKP,gBAAgB,CAAC,uDAA5D,mBAA8DQ,IAAI,GAClE,IAAI;EAERX,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,IAAIN,kBAAkB,EAAE;MACtB,MAAMO,OAAO,GACV,eAAcP,kBAAmB,+DAA8D,GAC/F,6HAA4H,GAC5H,gJAA+I;MAElJQ,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CAACP,kBAAkB,CAAC,CAAC;EAExB,OAAO;IAAEF;EAAoB,CAAC;AAChC"}