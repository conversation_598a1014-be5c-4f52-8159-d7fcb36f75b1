{"version": 3, "names": ["usePreventRemoveContext", "React", "useInvalidPreventRemoveError", "descriptors", "preventedRoutes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "preventedDescriptor", "isHeaderBackButtonMenuEnabledOnPreventedScreen", "options", "headerBackButtonMenuEnabled", "preventedRouteName", "route", "name", "useEffect", "message", "console", "error"], "sourceRoot": "../../../src", "sources": ["utils/useInvalidPreventRemoveError.tsx"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,0BAA0B;AAClE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAI9B,eAAe,SAASC,4BAA4B,CAClDC,WAAqC,EACrC;EAAA;EACA,MAAM;IAAEC;EAAgB,CAAC,GAAGJ,uBAAuB,EAAE;EACrD,MAAMK,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,CAAC;EACzD,MAAMI,mBAAmB,GAAGL,WAAW,CAACE,iBAAiB,CAAC;EAC1D,MAAMI,8CAA8C,GAClDD,mBAAmB,aAAnBA,mBAAmB,gDAAnBA,mBAAmB,CAAEE,OAAO,0DAA5B,sBAA8BC,2BAA2B;EAC3D,MAAMC,kBAAkB,GAAGJ,mBAAmB,aAAnBA,mBAAmB,iDAAnBA,mBAAmB,CAAEK,KAAK,2DAA1B,uBAA4BC,IAAI;EAE3Db,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,IACEV,iBAAiB,IAAI,IAAI,IACzBI,8CAA8C,EAC9C;MACA,MAAMO,OAAO,GACV,cAAaJ,kBAAmB,2GAA0G,GAC1I,8DAA6DA,kBAAmB,mCAAkC;MACrHK,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CACDX,iBAAiB,EACjBI,8CAA8C,EAC9CG,kBAAkB,CACnB,CAAC;AACJ"}