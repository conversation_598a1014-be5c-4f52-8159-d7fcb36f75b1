{"version": 3, "names": ["React", "Platform", "View", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DebugContainer", "props", "process", "env", "NODE_ENV", "stackPresentation", "rest", "OS"], "sourceRoot": "../../../src", "sources": ["views/DebugContainer.native.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,IAAI,QAAmB,cAAc;AACxD;AACA,OAAOC,YAAY,MAAM,iDAAiD;AAQ1E;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAIC,KAAqB,IAAK;EAC9C,oBAAO,oBAAC,IAAI,eAAKA,KAAK;IAAE,WAAW,EAAE;EAAM,GAAG;AAChD,CAAC;AAED,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,cAAc,GAAIC,KAAqB,IAAK;IAC1C,MAAM;MAAEI,iBAAiB;MAAE,GAAGC;IAAK,CAAC,GAAGL,KAAK;IAE5C,IAAIJ,QAAQ,CAACU,EAAE,KAAK,KAAK,IAAIF,iBAAiB,KAAK,MAAM,EAAE;MACzD;MACA,oBACE,oBAAC,YAAY,qBACX,oBAAC,IAAI,eAAKC,IAAI;QAAE,WAAW,EAAE;MAAM,GAAG,CACzB;IAEnB;IAEA,oBAAO,oBAAC,IAAI,eAAKA,IAAI;MAAE,WAAW,EAAE;IAAM,GAAG;EAC/C,CAAC;AACH;AAEA,eAAeN,cAAc"}