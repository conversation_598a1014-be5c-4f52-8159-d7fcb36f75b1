{"version": 3, "names": ["getHeaderTitle", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderBackContext", "SafeAreaProviderCompat", "Screen", "React", "Image", "StyleSheet", "View", "TRANSPARENT_PRESENTATIONS", "NativeStackView", "state", "descriptors", "parentHeaderBack", "useContext", "styles", "container", "routes", "map", "route", "i", "isFocused", "index", "previousKey", "key", "<PERSON><PERSON><PERSON>", "previousDescriptor", "undefined", "nextDescriptor", "options", "navigation", "render", "headerBack", "title", "name", "canGoBack", "header", "headerShown", "headerTintColor", "headerBackImageSource", "headerLeft", "headerRight", "headerTitle", "headerTitleAlign", "headerTitleStyle", "headerStyle", "headerShadowVisible", "headerTransparent", "headerBackground", "headerBackTitle", "presentation", "contentStyle", "nextPresentation", "back", "tintColor", "label", "backImage", "goBack", "children", "absoluteFill", "display", "includes", "backgroundColor", "contentContainer", "create", "flex", "height", "width", "margin", "resizeMode"], "sourceRoot": "../../../src", "sources": ["views/NativeStackView.tsx"], "mappings": "AAAA,SACEA,cAAc,EACdC,MAAM,EACNC,gBAAgB,EAChBC,iBAAiB,EACjBC,sBAAsB,EACtBC,MAAM,QACD,4BAA4B;AAKnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAetD,MAAMC,yBAAyB,GAAG,CAChC,kBAAkB,EAClB,2BAA2B,CAC5B;AAED,eAAe,SAASC,eAAe,OAAgC;EAAA,IAA/B;IAAEC,KAAK;IAAEC;EAAmB,CAAC;EACnE,MAAMC,gBAAgB,GAAGR,KAAK,CAACS,UAAU,CAACZ,iBAAiB,CAAC;EAE5D,oBACE,oBAAC,sBAAsB,qBACrB,oBAAC,IAAI;IAAC,KAAK,EAAEa,MAAM,CAACC;EAAU,GAC3BL,KAAK,CAACM,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;IAAA;IAC9B,MAAMC,SAAS,GAAGV,KAAK,CAACW,KAAK,KAAKF,CAAC;IACnC,MAAMG,WAAW,oBAAGZ,KAAK,CAACM,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,kDAAnB,cAAqBI,GAAG;IAC5C,MAAMC,OAAO,qBAAGd,KAAK,CAACM,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,mDAAnB,eAAqBI,GAAG;IACxC,MAAME,kBAAkB,GAAGH,WAAW,GAClCX,WAAW,CAACW,WAAW,CAAC,GACxBI,SAAS;IACb,MAAMC,cAAc,GAAGH,OAAO,GAAGb,WAAW,CAACa,OAAO,CAAC,GAAGE,SAAS;IACjE,MAAM;MAAEE,OAAO;MAAEC,UAAU;MAAEC;IAAO,CAAC,GAAGnB,WAAW,CAACO,KAAK,CAACK,GAAG,CAAC;IAE9D,MAAMQ,UAAU,GAAGN,kBAAkB,GACjC;MACEO,KAAK,EAAElC,cAAc,CACnB2B,kBAAkB,CAACG,OAAO,EAC1BH,kBAAkB,CAACP,KAAK,CAACe,IAAI;IAEjC,CAAC,GACDrB,gBAAgB;IAEpB,MAAMsB,SAAS,GAAGH,UAAU,KAAKL,SAAS;IAE1C,MAAM;MACJS,MAAM;MACNC,WAAW;MACXC,eAAe;MACfC,qBAAqB;MACrBC,UAAU;MACVC,WAAW;MACXC,WAAW;MACXC,gBAAgB;MAChBC,gBAAgB;MAChBC,WAAW;MACXC,mBAAmB;MACnBC,iBAAiB;MACjBC,gBAAgB;MAChBC,eAAe;MACfC,YAAY;MACZC;IACF,CAAC,GAAGtB,OAAO;IAEX,MAAMuB,gBAAgB,GAAGxB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEC,OAAO,CAACqB,YAAY;IAE7D,oBACE,oBAAC,MAAM;MACL,GAAG,EAAE/B,KAAK,CAACK,GAAI;MACf,OAAO,EAAEH,SAAU;MACnB,KAAK,EAAEF,KAAM;MACb,UAAU,EAAEW,UAAW;MACvB,WAAW,EAAEO,WAAY;MACzB,iBAAiB,EAAEU,iBAAkB;MACrC,MAAM,EACJX,MAAM,KAAKT,SAAS,GAClBS,MAAM,CAAC;QACLiB,IAAI,EAAErB,UAAU;QAChBH,OAAO;QACPV,KAAK;QACLW;MACF,CAAC,CAAC,gBAEF,oBAAC,MAAM;QACL,KAAK,EAAE/B,cAAc,CAAC8B,OAAO,EAAEV,KAAK,CAACe,IAAI,CAAE;QAC3C,eAAe,EAAEI,eAAgB;QACjC,UAAU,EACR,OAAOE,UAAU,KAAK,UAAU,GAC5B;UAAA,IAAC;YAAEc;UAAU,CAAC;UAAA,OACZd,UAAU,CAAC;YACTc,SAAS;YACTnB,SAAS;YACToB,KAAK,EAAEN;UACT,CAAC,CAAC;QAAA,IACJT,UAAU,KAAKb,SAAS,IAAIQ,SAAS,GACrC;UAAA,IAAC;YAAEmB;UAAU,CAAC;UAAA,oBACZ,oBAAC,gBAAgB;YACf,SAAS,EAAEA,SAAU;YACrB,SAAS,EACPf,qBAAqB,KAAKZ,SAAS,GAC/B,mBACE,oBAAC,KAAK;cACJ,MAAM,EAAEY,qBAAsB;cAC9B,KAAK,EAAE,CACLxB,MAAM,CAACyC,SAAS,EAChB;gBAAEF;cAAU,CAAC;YACb,EAEL,GACD3B,SACL;YACD,OAAO,EAAEG,UAAU,CAAC2B,MAAO;YAC3B,SAAS,EAAEtB;UAAU,EACrB;QAAA,CACH,GACDK,UACL;QACD,WAAW,EACT,OAAOC,WAAW,KAAK,UAAU,GAC7B;UAAA,IAAC;YAAEa;UAAU,CAAC;UAAA,OACZb,WAAW,CAAC;YAAEa,SAAS;YAAEnB;UAAU,CAAC,CAAC;QAAA,IACvCM,WACL;QACD,WAAW,EACT,OAAOC,WAAW,KAAK,UAAU,GAC7B;UAAA,IAAC;YAAEgB,QAAQ;YAAEJ;UAAU,CAAC;UAAA,OACtBZ,WAAW,CAAC;YAAEgB,QAAQ;YAAEJ;UAAU,CAAC,CAAC;QAAA,IACtCZ,WACL;QACD,gBAAgB,EAAEC,gBAAiB;QACnC,gBAAgB,EAAEC,gBAAiB;QACnC,iBAAiB,EAAEG,iBAAkB;QACrC,mBAAmB,EAAED,mBAAoB;QACzC,gBAAgB,EAAEE,gBAAiB;QACnC,WAAW,EAAEH;MAAY,EAG9B;MACD,KAAK,EAAE,CACLtC,UAAU,CAACoD,YAAY,EACvB;QACEC,OAAO,EACLvC,SAAS,IACR+B,gBAAgB,IAAI,IAAI,IACvB3C,yBAAyB,CAACoD,QAAQ,CAACT,gBAAgB,CAAE,GACnD,MAAM,GACN;MACR,CAAC,EACDF,YAAY,IAAI,IAAI,IACpBzC,yBAAyB,CAACoD,QAAQ,CAACX,YAAY,CAAC,GAC5C;QAAEY,eAAe,EAAE;MAAc,CAAC,GAClC,IAAI;IACR,gBAEF,oBAAC,iBAAiB,CAAC,QAAQ;MAAC,KAAK,EAAE9B;IAAW,gBAC5C,oBAAC,IAAI;MAAC,KAAK,EAAE,CAACjB,MAAM,CAACgD,gBAAgB,EAAEZ,YAAY;IAAE,GAClDpB,MAAM,EAAE,CACJ,CACoB,CACtB;EAEb,CAAC,CAAC,CACG,CACgB;AAE7B;AAEA,MAAMhB,MAAM,GAAGR,UAAU,CAACyD,MAAM,CAAC;EAC/BhD,SAAS,EAAE;IACTiD,IAAI,EAAE;EACR,CAAC;EACDF,gBAAgB,EAAE;IAChBE,IAAI,EAAE;EACR,CAAC;EACDT,SAAS,EAAE;IACTU,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;EACd;AACF,CAAC,CAAC"}