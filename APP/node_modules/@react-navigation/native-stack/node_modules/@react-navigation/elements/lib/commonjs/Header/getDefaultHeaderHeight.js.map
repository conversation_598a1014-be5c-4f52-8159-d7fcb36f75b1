{"version": 3, "names": ["getDefaultHeaderHeight", "layout", "modalPresentation", "statusBarHeight", "headerHeight", "isLandscape", "width", "height", "Platform", "OS", "isPad", "isTV"], "sourceRoot": "../../../src", "sources": ["Header/getDefaultHeaderHeight.tsx"], "mappings": ";;;;;;AAAA;AAIe,SAASA,sBAAsB,CAC5CC,MAAc,EACdC,iBAA0B,EAC1BC,eAAuB,EACf;EACR,IAAIC,YAAY;EAEhB,MAAMC,WAAW,GAAGJ,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACM,MAAM;EAEhD,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,IAAID,qBAAQ,CAACE,KAAK,IAAIF,qBAAQ,CAACG,IAAI,EAAE;MACnC,IAAIT,iBAAiB,EAAE;QACrBE,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACLA,YAAY,GAAG,EAAE;MACnB;IACF,CAAC,MAAM;MACL,IAAIC,WAAW,EAAE;QACfD,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIF,iBAAiB,EAAE;UACrBE,YAAY,GAAG,EAAE;QACnB,CAAC,MAAM;UACLA,YAAY,GAAG,EAAE;QACnB;MACF;IACF;EACF,CAAC,MAAM,IAAII,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IACpCL,YAAY,GAAG,EAAE;EACnB,CAAC,MAAM;IACLA,YAAY,GAAG,EAAE;EACnB;EAEA,OAAOA,YAAY,GAAGD,eAAe;AACvC"}