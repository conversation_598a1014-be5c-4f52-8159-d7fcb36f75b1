{"version": 3, "names": ["useHeaderHeight", "height", "React", "useContext", "HeaderHeightContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["Header/useHeaderHeight.tsx"], "mappings": ";;;;;;AAAA;AAEA;AAAwD;AAAA;AAAA;AAEzC,SAASA,eAAe,GAAG;EACxC,MAAMC,MAAM,GAAGC,KAAK,CAACC,UAAU,CAACC,4BAAmB,CAAC;EAEpD,IAAIH,MAAM,KAAKI,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,wFAAwF,CACzF;EACH;EAEA,OAAOL,MAAM;AACf"}