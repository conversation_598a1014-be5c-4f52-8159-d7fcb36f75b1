{"version": 3, "names": ["contexts", "global", "Map", "getNamedContext", "name", "initialValue", "context", "get", "React", "createContext", "displayName", "set"], "sourceRoot": "../../src", "sources": ["getNamedContext.tsx"], "mappings": ";;;;;;AAAA;AAA+B;AAAA;AAE/B,MAAMA,QAAQ,GAAG,uCAAuC;AAMxD;AACAC,MAAM,CAACD,QAAQ,CAAC,GAAGC,MAAM,CAACD,QAAQ,CAAC,IAAI,IAAIE,GAAG,EAA8B;AAE7D,SAASC,eAAe,CACrCC,IAAY,EACZC,YAAe,EACG;EAClB,IAAIC,OAAO,GAAGL,MAAM,CAACD,QAAQ,CAAC,CAACO,GAAG,CAACH,IAAI,CAAC;EAExC,IAAIE,OAAO,EAAE;IACX,OAAOA,OAAO;EAChB;EAEAA,OAAO,gBAAGE,KAAK,CAACC,aAAa,CAAIJ,YAAY,CAAC;EAC9CC,OAAO,CAACI,WAAW,GAAGN,IAAI;EAE1BH,MAAM,CAACD,QAAQ,CAAC,CAACW,GAAG,CAACP,IAAI,EAAEE,OAAO,CAAC;EAEnC,OAAOA,OAAO;AAChB"}