{"version": 3, "names": ["React", "Dimensions", "Platform", "StyleSheet", "View", "initialWindowMetrics", "SafeAreaFrameContext", "SafeAreaInsetsContext", "SafeAreaProvider", "width", "height", "get", "initialMetrics", "OS", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "useContext", "styles", "container", "SafeAreaFrameProvider", "element", "useRef", "set<PERSON>rame", "useState", "useEffect", "current", "rect", "getBoundingClientRect", "timeout", "observer", "ResizeObserver", "entries", "entry", "contentRect", "clearTimeout", "setTimeout", "observe", "disconnect", "absoluteFillObject", "pointerEvents", "visibility", "create", "flex"], "sourceRoot": "../../src", "sources": ["SafeAreaProviderCompat.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,UAAU,EACVC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SACEC,oBAAoB,EAEpBC,oBAAoB,EACpBC,qBAAqB,EACrBC,gBAAgB,QACX,gCAAgC;AAOvC,MAAM;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGT,UAAU,CAACU,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMC,cAAc,GAClBV,QAAQ,CAACW,EAAE,KAAK,KAAK,IAAIR,oBAAoB,IAAI,IAAI,GACjD;EACES,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEP,KAAK;IAAEC;EAAO,CAAC;EACpCO,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDhB,oBAAoB;AAE1B,eAAe,SAASiB,sBAAsB,OAA6B;EAAA,IAA5B;IAAEC,QAAQ;IAAEC;EAAa,CAAC;EACvE,MAAMP,MAAM,GAAGjB,KAAK,CAACyB,UAAU,CAAClB,qBAAqB,CAAC;EAEtD,IAAIU,MAAM,EAAE;IACV;IACA;IACA;IACA,oBAAO,oBAAC,IAAI;MAAC,KAAK,EAAE,CAACS,MAAM,CAACC,SAAS,EAAEH,KAAK;IAAE,GAAED,QAAQ,CAAQ;EAClE;EAEA,IAAIrB,QAAQ,CAACW,EAAE,KAAK,KAAK,EAAE;IACzBU,QAAQ,gBACN,oBAAC,qBAAqB;MAAC,cAAc,EAAEX;IAAe,GACnDW,QAAQ,CAEZ;EACH;EAEA,oBACE,oBAAC,gBAAgB;IAAC,cAAc,EAAEX,cAAe;IAAC,KAAK,EAAEY;EAAM,GAC5DD,QAAQ,CACQ;AAEvB;;AAEA;AACA;AACA,MAAMK,qBAAqB,GAAG,SAMxB;EAAA,IANyB;IAC7BhB,cAAc;IACdW;EAIF,CAAC;EACC,MAAMM,OAAO,GAAG7B,KAAK,CAAC8B,MAAM,CAAiB,IAAI,CAAC;EAClD,MAAM,CAAChB,KAAK,EAAEiB,QAAQ,CAAC,GAAG/B,KAAK,CAACgC,QAAQ,CAACpB,cAAc,CAACE,KAAK,CAAC;EAE9Dd,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,IAAIJ,OAAO,CAACK,OAAO,IAAI,IAAI,EAAE;MAC3B;IACF;IAEA,MAAMC,IAAI,GAAGN,OAAO,CAACK,OAAO,CAACE,qBAAqB,EAAE;IAEpDL,QAAQ,CAAC;MACPhB,CAAC,EAAEoB,IAAI,CAACpB,CAAC;MACTC,CAAC,EAAEmB,IAAI,CAACnB,CAAC;MACTP,KAAK,EAAE0B,IAAI,CAAC1B,KAAK;MACjBC,MAAM,EAAEyB,IAAI,CAACzB;IACf,CAAC,CAAC;IAEF,IAAI2B,OAAuB;IAE3B,MAAMC,QAAQ,GAAG,IAAIC,cAAc,CAAEC,OAAO,IAAK;MAC/C,MAAMC,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC;MAExB,IAAIC,KAAK,EAAE;QACT,MAAM;UAAE1B,CAAC;UAAEC,CAAC;UAAEP,KAAK;UAAEC;QAAO,CAAC,GAAG+B,KAAK,CAACC,WAAW;;QAEjD;QACAC,YAAY,CAACN,OAAO,CAAC;QACrBA,OAAO,GAAGO,UAAU,CAAC,MAAM;UACzBb,QAAQ,CAAC;YAAEhB,CAAC;YAAEC,CAAC;YAAEP,KAAK;YAAEC;UAAO,CAAC,CAAC;QACnC,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;IAEF4B,QAAQ,CAACO,OAAO,CAAChB,OAAO,CAACK,OAAO,CAAC;IAEjC,OAAO,MAAM;MACXI,QAAQ,CAACQ,UAAU,EAAE;MACrBH,YAAY,CAACN,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE,oBAAC,oBAAoB,CAAC,QAAQ;IAAC,KAAK,EAAEvB;EAAM,gBAC1C;IACE,GAAG,EAAEe,OAAQ;IACb,KAAK,EAAE;MACL,GAAG1B,UAAU,CAAC4C,kBAAkB;MAChCC,aAAa,EAAE,MAAM;MACrBC,UAAU,EAAE;IACd;EAAE,EACF,EACD1B,QAAQ,CACqB;AAEpC,CAAC;AAEDD,sBAAsB,CAACV,cAAc,GAAGA,cAAc;AAEtD,MAAMc,MAAM,GAAGvB,UAAU,CAAC+C,MAAM,CAAC;EAC/BvB,SAAS,EAAE;IACTwB,IAAI,EAAE;EACR;AACF,CAAC,CAAC"}