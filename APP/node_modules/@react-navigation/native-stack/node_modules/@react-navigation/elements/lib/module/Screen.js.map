{"version": 3, "names": ["NavigationContext", "NavigationRouteContext", "React", "StyleSheet", "View", "useSafeAreaFrame", "useSafeAreaInsets", "Background", "getDefaultHeaderHeight", "HeaderHeightContext", "HeaderShownContext", "Screen", "props", "dimensions", "insets", "isParentHeaderShown", "useContext", "parentHeaderHeight", "focused", "modal", "header", "headerShown", "headerTransparent", "headerStatusBarHeight", "top", "navigation", "route", "children", "style", "headerHeight", "setHeaderHeight", "useState", "styles", "container", "content", "e", "height", "nativeEvent", "layout", "absolute", "create", "flex", "flexDirection", "position", "left", "right"], "sourceRoot": "../../src", "sources": ["Screen.tsx"], "mappings": "AAAA,SACEA,iBAAiB,EAEjBC,sBAAsB,QAGjB,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAAoBC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AACrE,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AAEvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,sBAAsB,MAAM,iCAAiC;AACpE,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,kBAAkB,MAAM,6BAA6B;AAe5D,eAAe,SAASC,MAAM,CAACC,KAAY,EAAE;EAC3C,MAAMC,UAAU,GAAGR,gBAAgB,EAAE;EACrC,MAAMS,MAAM,GAAGR,iBAAiB,EAAE;EAElC,MAAMS,mBAAmB,GAAGb,KAAK,CAACc,UAAU,CAACN,kBAAkB,CAAC;EAChE,MAAMO,kBAAkB,GAAGf,KAAK,CAACc,UAAU,CAACP,mBAAmB,CAAC;EAEhE,MAAM;IACJS,OAAO;IACPC,KAAK,GAAG,KAAK;IACbC,MAAM;IACNC,WAAW,GAAG,IAAI;IAClBC,iBAAiB;IACjBC,qBAAqB,GAAGR,mBAAmB,GAAG,CAAC,GAAGD,MAAM,CAACU,GAAG;IAC5DC,UAAU;IACVC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGhB,KAAK;EAET,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAG5B,KAAK,CAAC6B,QAAQ,CAAC,MACrDvB,sBAAsB,CAACK,UAAU,EAAEM,KAAK,EAAEI,qBAAqB,CAAC,CACjE;EAED,oBACE,oBAAC,UAAU;IACT,2BAA2B,EAAE,CAACL,OAAQ;IACtC,yBAAyB,EAAEA,OAAO,GAAG,MAAM,GAAG,qBAAsB;IACpE,KAAK,EAAE,CAACc,MAAM,CAACC,SAAS,EAAEL,KAAK;EAAE,gBAEjC,oBAAC,IAAI;IAAC,KAAK,EAAEI,MAAM,CAACE;EAAQ,gBAC1B,oBAAC,kBAAkB,CAAC,QAAQ;IAC1B,KAAK,EAAEnB,mBAAmB,IAAIM,WAAW,KAAK;EAAM,gBAEpD,oBAAC,mBAAmB,CAAC,QAAQ;IAC3B,KAAK,EAAEA,WAAW,GAAGQ,YAAY,GAAGZ,kBAAkB,IAAI;EAAE,GAE3DU,QAAQ,CACoB,CACH,CACzB,EACNN,WAAW,gBACV,oBAAC,iBAAiB,CAAC,QAAQ;IAAC,KAAK,EAAEI;EAAW,gBAC5C,oBAAC,sBAAsB,CAAC,QAAQ;IAAC,KAAK,EAAEC;EAAM,gBAC5C,oBAAC,IAAI;IACH,QAAQ,EAAGS,CAAC,IAAK;MACf,MAAM;QAAEC;MAAO,CAAC,GAAGD,CAAC,CAACE,WAAW,CAACC,MAAM;MAEvCR,eAAe,CAACM,MAAM,CAAC;IACzB,CAAE;IACF,KAAK,EAAEd,iBAAiB,GAAGU,MAAM,CAACO,QAAQ,GAAG;EAAK,GAEjDnB,MAAM,CACF,CACyB,CACP,GAC3B,IAAI,CACG;AAEjB;AAEA,MAAMY,MAAM,GAAG7B,UAAU,CAACqC,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB,CAAC;EACD;EACAR,OAAO,EAAE;IACPO,IAAI,EAAE;EACR,CAAC;EACDF,QAAQ,EAAE;IACRI,QAAQ,EAAE,UAAU;IACpBnB,GAAG,EAAE,CAAC;IACNoB,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT;AACF,CAAC,CAAC"}