{"version": 3, "names": ["nanoid", "BaseRouter", "StackActions", "replace", "name", "params", "type", "payload", "push", "pop", "count", "popToTop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "router", "getInitialState", "routeNames", "routeParamList", "initialRouteName", "undefined", "includes", "stale", "key", "index", "routes", "getRehydratedState", "partialState", "state", "filter", "route", "map", "length", "getStateForRouteNamesChange", "routeKeyChanges", "Math", "min", "getStateForRouteFocus", "findIndex", "r", "slice", "getStateForAction", "action", "target", "source", "i", "getId", "routeGetIdList", "id", "find", "max", "concat", "path", "merge", "actionCreators"], "sourceRoot": "../../src", "sources": ["StackRouter.tsx"], "mappings": "AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAE1C,OAAOC,UAAU,MAAM,cAAc;AAiFrC,OAAO,MAAMC,YAAY,GAAG;EAC1BC,OAAO,CAACC,IAAY,EAAEC,MAAe,EAAmB;IACtD,OAAO;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;QAAEH,IAAI;QAAEC;MAAO;IAAE,CAAC;EACvD,CAAC;EACDG,IAAI,CAACJ,IAAY,EAAEC,MAAe,EAAmB;IACnD,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE;QAAEH,IAAI;QAAEC;MAAO;IAAE,CAAC;EACpD,CAAC;EACDI,GAAG,GAAqC;IAAA,IAApCC,KAAa,uEAAG,CAAC;IACnB,OAAO;MAAEJ,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE;QAAEG;MAAM;IAAE,CAAC;EAC5C,CAAC;EACDC,QAAQ,GAAoB;IAC1B,OAAO;MAAEL,IAAI,EAAE;IAAa,CAAC;EAC/B;AACF,CAAC;AAED,eAAe,SAASM,WAAW,CAACC,OAA2B,EAAE;EAC/D,MAAMC,MAGL,GAAG;IACF,GAAGb,UAAU;IAEbK,IAAI,EAAE,OAAO;IAEbS,eAAe,OAAiC;MAAA,IAAhC;QAAEC,UAAU;QAAEC;MAAe,CAAC;MAC5C,MAAMC,gBAAgB,GACpBL,OAAO,CAACK,gBAAgB,KAAKC,SAAS,IACtCH,UAAU,CAACI,QAAQ,CAACP,OAAO,CAACK,gBAAgB,CAAC,GACzCL,OAAO,CAACK,gBAAgB,GACxBF,UAAU,CAAC,CAAC,CAAC;MAEnB,OAAO;QACLK,KAAK,EAAE,KAAK;QACZf,IAAI,EAAE,OAAO;QACbgB,GAAG,EAAG,SAAQtB,MAAM,EAAG,EAAC;QACxBuB,KAAK,EAAE,CAAC;QACRP,UAAU;QACVQ,MAAM,EAAE,CACN;UACEF,GAAG,EAAG,GAAEJ,gBAAiB,IAAGlB,MAAM,EAAG,EAAC;UACtCI,IAAI,EAAEc,gBAAgB;UACtBb,MAAM,EAAEY,cAAc,CAACC,gBAAgB;QACzC,CAAC;MAEL,CAAC;IACH,CAAC;IAEDO,kBAAkB,CAACC,YAAY,SAAkC;MAAA,IAAhC;QAAEV,UAAU;QAAEC;MAAe,CAAC;MAC7D,IAAIU,KAAK,GAAGD,YAAY;MAExB,IAAIC,KAAK,CAACN,KAAK,KAAK,KAAK,EAAE;QACzB,OAAOM,KAAK;MACd;MAEA,MAAMH,MAAM,GAAGG,KAAK,CAACH,MAAM,CACxBI,MAAM,CAAEC,KAAK,IAAKb,UAAU,CAACI,QAAQ,CAACS,KAAK,CAACzB,IAAI,CAAC,CAAC,CAClD0B,GAAG,CACDD,KAAK,KACH;QACC,GAAGA,KAAK;QACRP,GAAG,EAAEO,KAAK,CAACP,GAAG,IAAK,GAAEO,KAAK,CAACzB,IAAK,IAAGJ,MAAM,EAAG,EAAC;QAC7CK,MAAM,EACJY,cAAc,CAACY,KAAK,CAACzB,IAAI,CAAC,KAAKe,SAAS,GACpC;UACE,GAAGF,cAAc,CAACY,KAAK,CAACzB,IAAI,CAAC;UAC7B,GAAGyB,KAAK,CAACxB;QACX,CAAC,GACDwB,KAAK,CAACxB;MACd,CAAC,CAAkB,CACtB;MAEH,IAAImB,MAAM,CAACO,MAAM,KAAK,CAAC,EAAE;QACvB,MAAMb,gBAAgB,GACpBL,OAAO,CAACK,gBAAgB,KAAKC,SAAS,GAClCN,OAAO,CAACK,gBAAgB,GACxBF,UAAU,CAAC,CAAC,CAAC;QAEnBQ,MAAM,CAAChB,IAAI,CAAC;UACVc,GAAG,EAAG,GAAEJ,gBAAiB,IAAGlB,MAAM,EAAG,EAAC;UACtCI,IAAI,EAAEc,gBAAgB;UACtBb,MAAM,EAAEY,cAAc,CAACC,gBAAgB;QACzC,CAAC,CAAC;MACJ;MAEA,OAAO;QACLG,KAAK,EAAE,KAAK;QACZf,IAAI,EAAE,OAAO;QACbgB,GAAG,EAAG,SAAQtB,MAAM,EAAG,EAAC;QACxBuB,KAAK,EAAEC,MAAM,CAACO,MAAM,GAAG,CAAC;QACxBf,UAAU;QACVQ;MACF,CAAC;IACH,CAAC;IAEDQ,2BAA2B,CACzBL,KAAK,SAEL;MAAA,IADA;QAAEX,UAAU;QAAEC,cAAc;QAAEgB;MAAgB,CAAC;MAE/C,MAAMT,MAAM,GAAGG,KAAK,CAACH,MAAM,CAACI,MAAM,CAC/BC,KAAK,IACJb,UAAU,CAACI,QAAQ,CAACS,KAAK,CAACzB,IAAI,CAAC,IAC/B,CAAC6B,eAAe,CAACb,QAAQ,CAACS,KAAK,CAACzB,IAAI,CAAC,CACxC;MAED,IAAIoB,MAAM,CAACO,MAAM,KAAK,CAAC,EAAE;QACvB,MAAMb,gBAAgB,GACpBL,OAAO,CAACK,gBAAgB,KAAKC,SAAS,IACtCH,UAAU,CAACI,QAAQ,CAACP,OAAO,CAACK,gBAAgB,CAAC,GACzCL,OAAO,CAACK,gBAAgB,GACxBF,UAAU,CAAC,CAAC,CAAC;QAEnBQ,MAAM,CAAChB,IAAI,CAAC;UACVc,GAAG,EAAG,GAAEJ,gBAAiB,IAAGlB,MAAM,EAAG,EAAC;UACtCI,IAAI,EAAEc,gBAAgB;UACtBb,MAAM,EAAEY,cAAc,CAACC,gBAAgB;QACzC,CAAC,CAAC;MACJ;MAEA,OAAO;QACL,GAAGS,KAAK;QACRX,UAAU;QACVQ,MAAM;QACND,KAAK,EAAEW,IAAI,CAACC,GAAG,CAACR,KAAK,CAACJ,KAAK,EAAEC,MAAM,CAACO,MAAM,GAAG,CAAC;MAChD,CAAC;IACH,CAAC;IAEDK,qBAAqB,CAACT,KAAK,EAAEL,GAAG,EAAE;MAChC,MAAMC,KAAK,GAAGI,KAAK,CAACH,MAAM,CAACa,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAChB,GAAG,KAAKA,GAAG,CAAC;MAE1D,IAAIC,KAAK,KAAK,CAAC,CAAC,IAAIA,KAAK,KAAKI,KAAK,CAACJ,KAAK,EAAE;QACzC,OAAOI,KAAK;MACd;MAEA,OAAO;QACL,GAAGA,KAAK;QACRJ,KAAK;QACLC,MAAM,EAAEG,KAAK,CAACH,MAAM,CAACe,KAAK,CAAC,CAAC,EAAEhB,KAAK,GAAG,CAAC;MACzC,CAAC;IACH,CAAC;IAEDiB,iBAAiB,CAACb,KAAK,EAAEc,MAAM,EAAE5B,OAAO,EAAE;MACxC,MAAM;QAAEI;MAAe,CAAC,GAAGJ,OAAO;MAElC,QAAQ4B,MAAM,CAACnC,IAAI;QACjB,KAAK,SAAS;UAAE;YACd,MAAMiB,KAAK,GACTkB,MAAM,CAACC,MAAM,KAAKf,KAAK,CAACL,GAAG,IAAImB,MAAM,CAACE,MAAM,GACxChB,KAAK,CAACH,MAAM,CAACa,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAChB,GAAG,KAAKmB,MAAM,CAACE,MAAM,CAAC,GACtDhB,KAAK,CAACJ,KAAK;YAEjB,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;cAChB,OAAO,IAAI;YACb;YAEA,MAAM;cAAEnB,IAAI;cAAEkB,GAAG;cAAEjB;YAAO,CAAC,GAAGoC,MAAM,CAAClC,OAAO;YAE5C,IAAI,CAACoB,KAAK,CAACX,UAAU,CAACI,QAAQ,CAAChB,IAAI,CAAC,EAAE;cACpC,OAAO,IAAI;YACb;YAEA,OAAO;cACL,GAAGuB,KAAK;cACRH,MAAM,EAAEG,KAAK,CAACH,MAAM,CAACM,GAAG,CAAC,CAACD,KAAK,EAAEe,CAAC,KAChCA,CAAC,KAAKrB,KAAK,GACP;gBACED,GAAG,EAAEA,GAAG,KAAKH,SAAS,GAAGG,GAAG,GAAI,GAAElB,IAAK,IAAGJ,MAAM,EAAG,EAAC;gBACpDI,IAAI;gBACJC,MAAM,EACJY,cAAc,CAACb,IAAI,CAAC,KAAKe,SAAS,GAC9B;kBACE,GAAGF,cAAc,CAACb,IAAI,CAAC;kBACvB,GAAGC;gBACL,CAAC,GACDA;cACR,CAAC,GACDwB,KAAK;YAEb,CAAC;UACH;QAEA,KAAK,MAAM;UACT,IAAIF,KAAK,CAACX,UAAU,CAACI,QAAQ,CAACqB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC,EAAE;YAClD,MAAMyC,KAAK,GAAGhC,OAAO,CAACiC,cAAc,CAACL,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC;YACzD,MAAM2C,EAAE,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;cAAExC,MAAM,EAAEoC,MAAM,CAAClC,OAAO,CAACF;YAAO,CAAC,CAAC;YAErD,MAAMwB,KAAK,GAAGkB,EAAE,GACZpB,KAAK,CAACH,MAAM,CAACwB,IAAI,CACdnB,KAAK,IACJA,KAAK,CAACzB,IAAI,KAAKqC,MAAM,CAAClC,OAAO,CAACH,IAAI,IAClC2C,EAAE,MAAKF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;cAAExC,MAAM,EAAEwB,KAAK,CAACxB;YAAO,CAAC,CAAC,EAC3C,GACDc,SAAS;YAEb,IAAIK,MAAuB;YAE3B,IAAIK,KAAK,EAAE;cACTL,MAAM,GAAGG,KAAK,CAACH,MAAM,CAACI,MAAM,CAAEU,CAAC,IAAKA,CAAC,CAAChB,GAAG,KAAKO,KAAK,CAACP,GAAG,CAAC;cACxDE,MAAM,CAAChB,IAAI,CAAC;gBACV,GAAGqB,KAAK;gBACRxB,MAAM,EACJY,cAAc,CAACwB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC,KAAKe,SAAS,GAC7C;kBACE,GAAGF,cAAc,CAACwB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC;kBACtC,GAAGqC,MAAM,CAAClC,OAAO,CAACF;gBACpB,CAAC,GACDoC,MAAM,CAAClC,OAAO,CAACF;cACvB,CAAC,CAAC;YACJ,CAAC,MAAM;cACLmB,MAAM,GAAG,CACP,GAAGG,KAAK,CAACH,MAAM,EACf;gBACEF,GAAG,EAAG,GAAEmB,MAAM,CAAClC,OAAO,CAACH,IAAK,IAAGJ,MAAM,EAAG,EAAC;gBACzCI,IAAI,EAAEqC,MAAM,CAAClC,OAAO,CAACH,IAAI;gBACzBC,MAAM,EACJY,cAAc,CAACwB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC,KAAKe,SAAS,GAC7C;kBACE,GAAGF,cAAc,CAACwB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC;kBACtC,GAAGqC,MAAM,CAAClC,OAAO,CAACF;gBACpB,CAAC,GACDoC,MAAM,CAAClC,OAAO,CAACF;cACvB,CAAC,CACF;YACH;YAEA,OAAO;cACL,GAAGsB,KAAK;cACRJ,KAAK,EAAEC,MAAM,CAACO,MAAM,GAAG,CAAC;cACxBP;YACF,CAAC;UACH;UAEA,OAAO,IAAI;QAEb,KAAK,KAAK;UAAE;YACV,MAAMD,KAAK,GACTkB,MAAM,CAACC,MAAM,KAAKf,KAAK,CAACL,GAAG,IAAImB,MAAM,CAACE,MAAM,GACxChB,KAAK,CAACH,MAAM,CAACa,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAChB,GAAG,KAAKmB,MAAM,CAACE,MAAM,CAAC,GACtDhB,KAAK,CAACJ,KAAK;YAEjB,IAAIA,KAAK,GAAG,CAAC,EAAE;cACb,MAAMb,KAAK,GAAGwB,IAAI,CAACe,GAAG,CAAC1B,KAAK,GAAGkB,MAAM,CAAClC,OAAO,CAACG,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;cAC3D,MAAMc,MAAM,GAAGG,KAAK,CAACH,MAAM,CACxBe,KAAK,CAAC,CAAC,EAAE7B,KAAK,CAAC,CACfwC,MAAM,CAACvB,KAAK,CAACH,MAAM,CAACe,KAAK,CAAChB,KAAK,GAAG,CAAC,CAAC,CAAC;cAExC,OAAO;gBACL,GAAGI,KAAK;gBACRJ,KAAK,EAAEC,MAAM,CAACO,MAAM,GAAG,CAAC;gBACxBP;cACF,CAAC;YACH;YAEA,OAAO,IAAI;UACb;QAEA,KAAK,YAAY;UACf,OAAOV,MAAM,CAAC0B,iBAAiB,CAC7Bb,KAAK,EACL;YACErB,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE;cAAEG,KAAK,EAAEiB,KAAK,CAACH,MAAM,CAACO,MAAM,GAAG;YAAE;UAC5C,CAAC,EACDlB,OAAO,CACR;QAEH,KAAK,UAAU;UACb,IACE4B,MAAM,CAAClC,OAAO,CAACH,IAAI,KAAKe,SAAS,IACjC,CAACQ,KAAK,CAACX,UAAU,CAACI,QAAQ,CAACqB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC,EAC/C;YACA,OAAO,IAAI;UACb;UAEA,IAAIqC,MAAM,CAAClC,OAAO,CAACe,GAAG,IAAImB,MAAM,CAAClC,OAAO,CAACH,IAAI,EAAE;YAC7C;YACA,IAAImB,KAAK,GAAG,CAAC,CAAC;YAEd,MAAMsB,KAAK;YACT;YACAJ,MAAM,CAAClC,OAAO,CAACe,GAAG,KAAKH,SAAS,IAChCsB,MAAM,CAAClC,OAAO,CAACH,IAAI,KAAKe,SAAS,GAC7BN,OAAO,CAACiC,cAAc,CAACL,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC,GAC3Ce,SAAS;YACf,MAAM4B,EAAE,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;cAAExC,MAAM,EAAEoC,MAAM,CAAClC,OAAO,CAACF;YAAO,CAAC,CAAC;YAErD,IAAI0C,EAAE,EAAE;cACNxB,KAAK,GAAGI,KAAK,CAACH,MAAM,CAACa,SAAS,CAC3BR,KAAK,IACJA,KAAK,CAACzB,IAAI,KAAKqC,MAAM,CAAClC,OAAO,CAACH,IAAI,IAClC2C,EAAE,MAAKF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG;gBAAExC,MAAM,EAAEwB,KAAK,CAACxB;cAAO,CAAC,CAAC,EAC3C;YACH,CAAC,MAAM,IACJsB,KAAK,CAACH,MAAM,CAACG,KAAK,CAACJ,KAAK,CAAC,CAACnB,IAAI,KAAKqC,MAAM,CAAClC,OAAO,CAACH,IAAI,IACrDqC,MAAM,CAAClC,OAAO,CAACe,GAAG,KAAKH,SAAS,IAClCQ,KAAK,CAACH,MAAM,CAACG,KAAK,CAACJ,KAAK,CAAC,CAACD,GAAG,KAAKmB,MAAM,CAAClC,OAAO,CAACe,GAAG,EACpD;cACAC,KAAK,GAAGI,KAAK,CAACJ,KAAK;YACrB,CAAC,MAAM;cACL,KAAK,IAAIqB,CAAC,GAAGjB,KAAK,CAACH,MAAM,CAACO,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;gBACjD,IACGjB,KAAK,CAACH,MAAM,CAACoB,CAAC,CAAC,CAACxC,IAAI,KAAKqC,MAAM,CAAClC,OAAO,CAACH,IAAI,IAC3CqC,MAAM,CAAClC,OAAO,CAACe,GAAG,KAAKH,SAAS,IAClCQ,KAAK,CAACH,MAAM,CAACoB,CAAC,CAAC,CAACtB,GAAG,KAAKmB,MAAM,CAAClC,OAAO,CAACe,GAAG,EAC1C;kBACAC,KAAK,GAAGqB,CAAC;kBACT;gBACF;cACF;YACF;YAEA,IACErB,KAAK,KAAK,CAAC,CAAC,IACZkB,MAAM,CAAClC,OAAO,CAACe,GAAG,IAClBmB,MAAM,CAAClC,OAAO,CAACH,IAAI,KAAKe,SAAS,EACjC;cACA,OAAO,IAAI;YACb;YAEA,IAAII,KAAK,KAAK,CAAC,CAAC,IAAIkB,MAAM,CAAClC,OAAO,CAACH,IAAI,KAAKe,SAAS,EAAE;cACrD,MAAMK,MAAM,GAAG,CACb,GAAGG,KAAK,CAACH,MAAM,EACf;gBACEF,GAAG,EACDmB,MAAM,CAAClC,OAAO,CAACe,GAAG,IAAK,GAAEmB,MAAM,CAAClC,OAAO,CAACH,IAAK,IAAGJ,MAAM,EAAG,EAAC;gBAC5DI,IAAI,EAAEqC,MAAM,CAAClC,OAAO,CAACH,IAAI;gBACzB+C,IAAI,EAAEV,MAAM,CAAClC,OAAO,CAAC4C,IAAI;gBACzB9C,MAAM,EACJY,cAAc,CAACwB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC,KAAKe,SAAS,GAC7C;kBACE,GAAGF,cAAc,CAACwB,MAAM,CAAClC,OAAO,CAACH,IAAI,CAAC;kBACtC,GAAGqC,MAAM,CAAClC,OAAO,CAACF;gBACpB,CAAC,GACDoC,MAAM,CAAClC,OAAO,CAACF;cACvB,CAAC,CACF;cAED,OAAO;gBACL,GAAGsB,KAAK;gBACRH,MAAM;gBACND,KAAK,EAAEC,MAAM,CAACO,MAAM,GAAG;cACzB,CAAC;YACH;YAEA,MAAMF,KAAK,GAAGF,KAAK,CAACH,MAAM,CAACD,KAAK,CAAC;YAEjC,IAAIlB,MAAM;YAEV,IAAIoC,MAAM,CAAClC,OAAO,CAAC6C,KAAK,EAAE;cACxB/C,MAAM,GACJoC,MAAM,CAAClC,OAAO,CAACF,MAAM,KAAKc,SAAS,IACnCF,cAAc,CAACY,KAAK,CAACzB,IAAI,CAAC,KAAKe,SAAS,GACpC;gBACE,GAAGF,cAAc,CAACY,KAAK,CAACzB,IAAI,CAAC;gBAC7B,GAAGyB,KAAK,CAACxB,MAAM;gBACf,GAAGoC,MAAM,CAAClC,OAAO,CAACF;cACpB,CAAC,GACDwB,KAAK,CAACxB,MAAM;YACpB,CAAC,MAAM;cACLA,MAAM,GACJY,cAAc,CAACY,KAAK,CAACzB,IAAI,CAAC,KAAKe,SAAS,GACpC;gBACE,GAAGF,cAAc,CAACY,KAAK,CAACzB,IAAI,CAAC;gBAC7B,GAAGqC,MAAM,CAAClC,OAAO,CAACF;cACpB,CAAC,GACDoC,MAAM,CAAClC,OAAO,CAACF,MAAM;YAC7B;YAEA,OAAO;cACL,GAAGsB,KAAK;cACRJ,KAAK;cACLC,MAAM,EAAE,CACN,GAAGG,KAAK,CAACH,MAAM,CAACe,KAAK,CAAC,CAAC,EAAEhB,KAAK,CAAC,EAC/BlB,MAAM,KAAKwB,KAAK,CAACxB,MAAM,IACtBoC,MAAM,CAAClC,OAAO,CAAC4C,IAAI,IAAIV,MAAM,CAAClC,OAAO,CAAC4C,IAAI,KAAKtB,KAAK,CAACsB,IAAK,GACvD;gBACE,GAAGtB,KAAK;gBACRsB,IAAI,EAAEV,MAAM,CAAClC,OAAO,CAAC4C,IAAI,IAAItB,KAAK,CAACsB,IAAI;gBACvC9C;cACF,CAAC,GACDsB,KAAK,CAACH,MAAM,CAACD,KAAK,CAAC;YAE3B,CAAC;UACH;UAEA,OAAO,IAAI;QAEb,KAAK,SAAS;UACZ,IAAII,KAAK,CAACJ,KAAK,GAAG,CAAC,EAAE;YACnB,OAAOT,MAAM,CAAC0B,iBAAiB,CAC7Bb,KAAK,EACL;cACErB,IAAI,EAAE,KAAK;cACXC,OAAO,EAAE;gBAAEG,KAAK,EAAE;cAAE,CAAC;cACrBgC,MAAM,EAAED,MAAM,CAACC,MAAM;cACrBC,MAAM,EAAEF,MAAM,CAACE;YACjB,CAAC,EACD9B,OAAO,CACR;UACH;UAEA,OAAO,IAAI;QAEb;UACE,OAAOZ,UAAU,CAACuC,iBAAiB,CAACb,KAAK,EAAEc,MAAM,CAAC;MAAC;IAEzD,CAAC;IAEDY,cAAc,EAAEnD;EAClB,CAAC;EAED,OAAOY,MAAM;AACf"}