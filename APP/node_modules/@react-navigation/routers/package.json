{"name": "@react-navigation/routers", "description": "Routers to help build custom navigators", "version": "6.1.9", "keywords": ["react", "react-native", "react-navigation"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/routers"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/custom-routers/", "main": "lib/commonjs/index.js", "react-native": "src/index.tsx", "source": "src/index.tsx", "module": "lib/module/index.js", "types": "lib/typescript/src/index.d.ts", "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"nanoid": "^3.1.23"}, "devDependencies": {"del-cli": "^5.0.0", "react-native-builder-bob": "^0.20.4", "typescript": "^4.9.4"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "cf836cb84932638369f7bf59534d5a3ccea9b4f6"}