import React from 'react';
import { Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import {
  IconFamily,
  IconSize,
  IconSizes,
  MaterialIcons,
  EmojiIcons,
  getIconSize,
} from '../../constants/icons';

// ===========================================
// 🎨 EXPRESSIVE ICON COMPONENT
// ===========================================

interface ExpressiveIconProps {
  // Icon identification
  family?: IconFamily;
  name: string;
  
  // Appearance
  size?: IconSize | number;
  color?: string;
  
  // Styling
  style?: ViewStyle | TextStyle;
  
  // Accessibility
  accessibilityLabel?: string;
  testID?: string;
  
  // Theming
  variant?: 'primary' | 'secondary' | 'tertiary' | 'surface' | 'error' | 'warning' | 'success';
}

export const ExpressiveIcon: React.FC<ExpressiveIconProps> = ({
  family = 'material',
  name,
  size = 'md',
  color,
  style,
  accessibilityLabel,
  testID,
  variant,
}) => {
  const theme = useDynamicTheme();

  // Get icon size
  const iconSize = getIconSize(size);

  // Get icon color based on variant or use provided color
  const getIconColor = (): string => {
    if (color) return color;
    
    switch (variant) {
      case 'primary':
        return theme.colors.primary;
      case 'secondary':
        return theme.colors.secondary;
      case 'tertiary':
        return theme.colors.tertiary;
      case 'surface':
        return theme.colors.onSurface;
      case 'error':
        return theme.colors.error;
      case 'warning':
        return theme.colors.error; // Using error color for warning as Material 3 doesn't have warning
      case 'success':
        return theme.colors.primary; // Using primary for success
      default:
        return theme.colors.onSurface;
    }
  };

  const iconColor = getIconColor();

  // Render based on icon family
  const renderIcon = () => {
    switch (family) {
      case 'emoji':
        return (
          <Text
            style={[
              styles.emojiIcon,
              {
                fontSize: iconSize,
                lineHeight: iconSize * 1.2,
              },
              style,
            ]}
            accessibilityLabel={accessibilityLabel || name}
            testID={testID}
          >
            {name}
          </Text>
        );

      case 'material':
        // For now, we'll use emoji fallbacks for Material icons
        // In a real implementation, you'd use react-native-vector-icons
        const materialIconFallbacks: Record<string, string> = {
          [MaterialIcons.home]: '🏠',
          [MaterialIcons.homeOutlined]: '🏡',
          [MaterialIcons.timer]: '⏱️',
          [MaterialIcons.play]: '▶️',
          [MaterialIcons.pause]: '⏸️',
          [MaterialIcons.stop]: '⏹️',
          [MaterialIcons.refresh]: '🔄',
          [MaterialIcons.task]: '📋',
          [MaterialIcons.check]: '✅',
          [MaterialIcons.add]: '➕',
          [MaterialIcons.remove]: '➖',
          [MaterialIcons.edit]: '✏️',
          [MaterialIcons.delete]: '🗑️',
          [MaterialIcons.analytics]: '📊',
          [MaterialIcons.person]: '👤',
          [MaterialIcons.people]: '👥',
          [MaterialIcons.settings]: '⚙️',
          [MaterialIcons.notification]: '🔔',
          [MaterialIcons.search]: '🔍',
          [MaterialIcons.menu]: '☰',
          [MaterialIcons.close]: '✖️',
          [MaterialIcons.back]: '⬅️',
          [MaterialIcons.forward]: '➡️',
          [MaterialIcons.up]: '⬆️',
          [MaterialIcons.down]: '⬇️',
          [MaterialIcons.favorite]: '❤️',
          [MaterialIcons.star]: '⭐',
          [MaterialIcons.share]: '📤',
          [MaterialIcons.info]: 'ℹ️',
          [MaterialIcons.warning]: '⚠️',
          [MaterialIcons.error]: '❌',
          [MaterialIcons.success]: '✅',
          [MaterialIcons.help]: '❓',
          [MaterialIcons.visibility]: '👁️',
          [MaterialIcons.visibilityOff]: '🙈',
          [MaterialIcons.lock]: '🔒',
          [MaterialIcons.lockOpen]: '🔓',
          [MaterialIcons.more]: '⋮',
          [MaterialIcons.moreHoriz]: '⋯',
          [MaterialIcons.expand]: '⌄',
          [MaterialIcons.collapse]: '⌃',
          [MaterialIcons.chevronLeft]: '‹',
          [MaterialIcons.chevronRight]: '›',
          [MaterialIcons.chevronUp]: '⌃',
          [MaterialIcons.chevronDown]: '⌄',
        };

        const fallbackEmoji = materialIconFallbacks[name] || '❓';
        
        return (
          <Text
            style={[
              styles.materialIcon,
              {
                fontSize: iconSize,
                lineHeight: iconSize * 1.2,
                color: iconColor,
              },
              style,
            ]}
            accessibilityLabel={accessibilityLabel || name}
            testID={testID}
          >
            {fallbackEmoji}
          </Text>
        );

      case 'custom':
        // For custom icons, you would implement SVG or other custom rendering
        return (
          <Text
            style={[
              styles.customIcon,
              {
                fontSize: iconSize,
                lineHeight: iconSize * 1.2,
                color: iconColor,
              },
              style,
            ]}
            accessibilityLabel={accessibilityLabel || name}
            testID={testID}
          >
            {name}
          </Text>
        );

      default:
        return null;
    }
  };

  const styles = StyleSheet.create({
    emojiIcon: {
      textAlign: 'center',
      includeFontPadding: false,
      textAlignVertical: 'center',
    },
    materialIcon: {
      textAlign: 'center',
      includeFontPadding: false,
      textAlignVertical: 'center',
      fontWeight: '400',
    },
    customIcon: {
      textAlign: 'center',
      includeFontPadding: false,
      textAlignVertical: 'center',
    },
  });

  return renderIcon();
};

export default ExpressiveIcon;
