import React, { useRef } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  ViewStyle,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { getElevationStyle, ColoredElevation } from '../../constants/surfaceElevation';
import { ExpressiveEasing, ExpressiveDurations } from '../../constants/expressiveMotion';
import ExpressiveIcon from './ExpressiveIcon';
import {
  IconFamily,
  IconSize,
} from '../../constants/icons';

// ===========================================
// 🔘 EXPRESSIVE ICON BUTTON COMPONENT
// ===========================================

export type IconButtonVariant = 'standard' | 'filled' | 'tonal' | 'outlined';
export type IconButtonSize = 'small' | 'medium' | 'large';

interface ExpressiveIconButtonProps {
  // Icon props
  iconFamily?: IconFamily;
  iconName: string;
  
  // Button props
  variant?: IconButtonVariant;
  size?: IconButtonSize;
  disabled?: boolean;
  loading?: boolean;
  
  // Appearance
  colorVariant?: keyof typeof ColoredElevation;
  backgroundColor?: string;
  iconColor?: string;
  
  // Interaction
  onPress?: () => void;
  onLongPress?: () => void;
  
  // Styling
  style?: ViewStyle;
  
  // Accessibility
  accessibilityLabel?: string;
  testID?: string;
}

export const ExpressiveIconButton: React.FC<ExpressiveIconButtonProps> = ({
  iconFamily = 'material',
  iconName,
  variant = 'standard',
  size = 'medium',
  disabled = false,
  loading = false,
  colorVariant = 'primary',
  backgroundColor,
  iconColor,
  onPress,
  onLongPress,
  style,
  accessibilityLabel,
  testID,
}) => {
  const theme = useDynamicTheme();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const elevationAnim = useRef(new Animated.Value(0)).current;

  // Handle press animations
  const handlePressIn = () => {
    if (disabled || loading) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.92,
        duration: ExpressiveDurations.micro,
        easing: ExpressiveEasing.standard,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: variant === 'filled' ? 2 : 0,
        duration: ExpressiveDurations.micro,
        easing: ExpressiveEasing.standard,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (disabled || loading) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: ExpressiveDurations.short,
        easing: ExpressiveEasing.bounce,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: 0,
        duration: ExpressiveDurations.short,
        easing: ExpressiveEasing.standard,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Get button colors based on variant
  const getButtonColors = () => {
    const colors = theme.colors;
    
    switch (variant) {
      case 'filled':
        return {
          backgroundColor: backgroundColor || (disabled ? colors.surfaceDisabled : colors.primary),
          iconColor: iconColor || (disabled ? colors.onSurfaceDisabled : colors.onPrimary),
          borderColor: 'transparent',
        };
      
      case 'tonal':
        return {
          backgroundColor: backgroundColor || (disabled ? colors.surfaceDisabled : colors.secondaryContainer),
          iconColor: iconColor || (disabled ? colors.onSurfaceDisabled : colors.onSecondaryContainer),
          borderColor: 'transparent',
        };
      
      case 'outlined':
        return {
          backgroundColor: backgroundColor || 'transparent',
          iconColor: iconColor || (disabled ? colors.onSurfaceDisabled : colors.onSurface),
          borderColor: disabled ? colors.outline : colors.outline,
        };
      
      default: // standard
        return {
          backgroundColor: backgroundColor || 'transparent',
          iconColor: iconColor || (disabled ? colors.onSurfaceDisabled : colors.onSurface),
          borderColor: 'transparent',
        };
    }
  };

  // Get button size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          width: 32,
          height: 32,
          borderRadius: 8,
          iconSize: 'sm' as IconSize,
        };
      
      case 'large':
        return {
          width: 56,
          height: 56,
          borderRadius: 16,
          iconSize: 'xl' as IconSize,
        };
      
      default: // medium
        return {
          width: 44,
          height: 44,
          borderRadius: 12,
          iconSize: 'lg' as IconSize,
        };
    }
  };

  const buttonColors = getButtonColors();
  const sizeStyles = getSizeStyles();

  const styles = StyleSheet.create({
    container: {
      width: sizeStyles.width,
      height: sizeStyles.height,
      borderRadius: sizeStyles.borderRadius,
      backgroundColor: buttonColors.backgroundColor,
      borderWidth: variant === 'outlined' ? 1 : 0,
      borderColor: buttonColors.borderColor,
      alignItems: 'center',
      justifyContent: 'center',
      opacity: disabled ? 0.6 : 1,
      ...getElevationStyle(variant === 'filled' ? 1 : 0, colorVariant),
    },
    loadingContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
  });

  const renderContent = () => {
    if (loading) {
      return (
        <ExpressiveIcon
          family="emoji"
          name="⏳"
          size={sizeStyles.iconSize}
          color={buttonColors.iconColor}
        />
      );
    }

    return (
      <ExpressiveIcon
        family={iconFamily}
        name={iconName}
        size={sizeStyles.iconSize}
        color={buttonColors.iconColor}
        accessibilityLabel={accessibilityLabel}
      />
    );
  };

  return (
    <Animated.View
      style={[
        { transform: [{ scale: scaleAnim }] },
        variant === 'filled' && {
          ...getElevationStyle(1, colorVariant),
          shadowOffset: {
            width: 0,
            height: elevationAnim,
          },
          shadowRadius: elevationAnim.interpolate({
            inputRange: [0, 2],
            outputRange: [0, 8],
          }),
        },
      ]}
    >
      <TouchableOpacity
        style={[styles.container, style]}
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.8}
        accessibilityLabel={accessibilityLabel || `${iconName} button`}
        accessibilityRole="button"
        testID={testID}
      >
        {renderContent()}
      </TouchableOpacity>
    </Animated.View>
  );
};

export default ExpressiveIconButton;
