import React, { createContext, useContext, ReactNode } from 'react';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import {
  IconFamily,
  IconSize,
  IconSizes,
  MaterialIcons,
  EmojiIcons,
  IconSets,
  getIconSize,
  getIconConfig,
} from '../../constants/icons';

// ===========================================
// 🎨 ICON PROVIDER CONTEXT
// ===========================================

interface IconTheme {
  // Default icon properties
  defaultFamily: IconFamily;
  defaultSize: IconSize;
  
  // Theme-aware colors
  primaryColor: string;
  secondaryColor: string;
  surfaceColor: string;
  onSurfaceColor: string;
  errorColor: string;
  
  // Icon mappings for different themes
  iconMappings: Record<string, { light: string; dark: string }>;
  
  // Utility functions
  getThemedIcon: (iconName: string) => string;
  getIconColor: (variant?: string) => string;
}

interface IconProviderProps {
  children: ReactNode;
  defaultFamily?: IconFamily;
  defaultSize?: IconSize;
  customMappings?: Record<string, { light: string; dark: string }>;
}

const IconContext = createContext<IconTheme | null>(null);

export const IconProvider: React.FC<IconProviderProps> = ({
  children,
  defaultFamily = 'material',
  defaultSize = 'md',
  customMappings = {},
}) => {
  const theme = useDynamicTheme();

  // Default icon mappings for light/dark themes
  const defaultIconMappings = {
    // Navigation icons
    home: {
      light: MaterialIcons.homeOutlined,
      dark: MaterialIcons.home,
    },
    
    // Status icons
    notification: {
      light: MaterialIcons.notificationOutline,
      dark: MaterialIcons.notification,
    },
    
    // User icons
    profile: {
      light: MaterialIcons.personOutline,
      dark: MaterialIcons.person,
    },
    
    // Action icons
    favorite: {
      light: MaterialIcons.favoriteOutline,
      dark: MaterialIcons.favorite,
    },
    
    // Settings icons
    settings: {
      light: MaterialIcons.settingsOutline,
      dark: MaterialIcons.settings,
    },
    
    // Brightness icons
    brightness: {
      light: MaterialIcons.lightMode,
      dark: MaterialIcons.darkMode,
    },
    
    // Info icons
    info: {
      light: MaterialIcons.infoOutline,
      dark: MaterialIcons.info,
    },
    
    // Warning icons
    warning: {
      light: MaterialIcons.warningOutline,
      dark: MaterialIcons.warning,
    },
    
    // Error icons
    error: {
      light: MaterialIcons.errorOutline,
      dark: MaterialIcons.error,
    },
    
    // Help icons
    help: {
      light: MaterialIcons.helpOutline,
      dark: MaterialIcons.help,
    },
    
    // Folder icons
    folder: {
      light: MaterialIcons.folderOutline,
      dark: MaterialIcons.folder,
    },
    
    // Message icons
    message: {
      light: MaterialIcons.messageOutline,
      dark: MaterialIcons.message,
    },
    
    // Chat icons
    chat: {
      light: MaterialIcons.chatOutline,
      dark: MaterialIcons.chat,
    },
    
    // Bookmark icons
    bookmark: {
      light: MaterialIcons.bookmarkOutline,
      dark: MaterialIcons.bookmark,
    },
    
    // Star icons
    star: {
      light: MaterialIcons.starOutline,
      dark: MaterialIcons.star,
    },
    
    // Check circle icons
    checkCircle: {
      light: MaterialIcons.checkCircleOutline,
      dark: MaterialIcons.checkCircle,
    },
    
    // Add circle icons
    addCircle: {
      light: MaterialIcons.addCircleOutline,
      dark: MaterialIcons.addCircle,
    },
    
    // Account icons
    account: {
      light: MaterialIcons.accountOutline,
      dark: MaterialIcons.account,
    },
    
    // People icons
    people: {
      light: MaterialIcons.peopleOutline,
      dark: MaterialIcons.people,
    },
    
    // Image icons
    image: {
      light: MaterialIcons.imageOutline,
      dark: MaterialIcons.image,
    },
    
    // Video icons
    video: {
      light: MaterialIcons.videoOutline,
      dark: MaterialIcons.video,
    },
    
    // File icons
    file: {
      light: MaterialIcons.fileOutline,
      dark: MaterialIcons.file,
    },
    
    // Delete icons
    delete: {
      light: MaterialIcons.deleteOutline,
      dark: MaterialIcons.delete,
    },
  };

  // Merge default and custom mappings
  const iconMappings = { ...defaultIconMappings, ...customMappings };

  // Get themed icon based on current theme
  const getThemedIcon = (iconName: string): string => {
    const mapping = iconMappings[iconName];
    if (!mapping) return iconName;
    
    return theme.isDark ? mapping.dark : mapping.light;
  };

  // Get icon color based on variant
  const getIconColor = (variant?: string): string => {
    switch (variant) {
      case 'primary':
        return theme.colors.primary;
      case 'secondary':
        return theme.colors.secondary;
      case 'tertiary':
        return theme.colors.tertiary;
      case 'surface':
        return theme.colors.onSurface;
      case 'surfaceVariant':
        return theme.colors.onSurfaceVariant;
      case 'error':
        return theme.colors.error;
      case 'success':
        return theme.colors.primary; // Using primary for success
      case 'warning':
        return theme.colors.error; // Using error for warning
      case 'disabled':
        return theme.colors.onSurfaceDisabled;
      default:
        return theme.colors.onSurface;
    }
  };

  const iconTheme: IconTheme = {
    // Default properties
    defaultFamily,
    defaultSize,
    
    // Theme colors
    primaryColor: theme.colors.primary,
    secondaryColor: theme.colors.secondary,
    surfaceColor: theme.colors.surface,
    onSurfaceColor: theme.colors.onSurface,
    errorColor: theme.colors.error,
    
    // Icon mappings
    iconMappings,
    
    // Utility functions
    getThemedIcon,
    getIconColor,
  };

  return (
    <IconContext.Provider value={iconTheme}>
      {children}
    </IconContext.Provider>
  );
};

// Hook to use icon theme
export const useIconTheme = (): IconTheme => {
  const context = useContext(IconContext);
  if (!context) {
    throw new Error('useIconTheme must be used within an IconProvider');
  }
  return context;
};

// Utility hook for getting themed icons
export const useThemedIcon = (iconName: string): string => {
  const iconTheme = useIconTheme();
  return iconTheme.getThemedIcon(iconName);
};

// Utility hook for getting icon colors
export const useIconColor = (variant?: string): string => {
  const iconTheme = useIconTheme();
  return iconTheme.getIconColor(variant);
};

export default IconProvider;
