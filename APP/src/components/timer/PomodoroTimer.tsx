import React, { useEffect, useRef, useCallback } from 'react';
import {
  View,
  StyleSheet,
  AppState,
  AppStateStatus,
  Vibration,
} from 'react-native';
import { Text, Button, IconButton, Surface } from 'react-native-paper';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { useTimerStore } from '../../stores/timerStore';
import { formatTime, getNextPomodoroPhase, getPhaseDuration } from '../../utils/timerUtils';
import ExpressiveTimerCard from './ExpressiveTimerCard';

interface PomodoroTimerProps {
  onSessionComplete?: (sessionData: any) => void;
  onPhaseChange?: (phase: string) => void;
}

const PomodoroTimer: React.FC<PomodoroTimerProps> = ({
  onSessionComplete,
  onPhaseChange,
}) => {
  const theme = useDynamicTheme();
  const {
    status,
    mode,
    currentPhase,
    displayTime,
    elapsedTime,
    pomodoroCount,
    workDuration,
    shortBreakDuration,
    longBreakDuration,
    longBreakInterval,
    autoStartBreaks,
    autoStartPomodoros,
    soundEnabled,
    vibrationEnabled,
    selectedSubject,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    skipPhase,
    updateDisplayTime,
    updateElapsedTime,
    completeSession,
    setMode,
  } = useTimerStore();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const backgroundTimeRef = useRef<number>(0);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Set mode to pomodoro on mount
  useEffect(() => {
    setMode('pomodoro');
  }, [setMode]);

  // Timer tick logic
  const tick = useCallback(() => {
    if (status === 'running') {
      const totalDuration = getPhaseDuration(currentPhase, {
        workDuration,
        shortBreakDuration,
        longBreakDuration,
      });

      const newElapsedTime = elapsedTime + 1;
      const newDisplayTime = Math.max(totalDuration - newElapsedTime, 0);

      updateElapsedTime(newElapsedTime);
      updateDisplayTime(newDisplayTime);

      // Check if phase is complete
      if (newDisplayTime === 0) {
        handlePhaseComplete();
      }
    }
  }, [
    status,
    currentPhase,
    elapsedTime,
    workDuration,
    shortBreakDuration,
    longBreakDuration,
    updateElapsedTime,
    updateDisplayTime,
  ]);

  // Handle phase completion
  const handlePhaseComplete = useCallback(() => {
    // Vibration feedback
    if (vibrationEnabled) {
      Vibration.vibrate([0, 500, 200, 500]);
    }

    // Complete current session if it's a work phase
    if (currentPhase === 'work') {
      const sessionData = {
        subject: selectedSubject || 'Unknown',
        duration: elapsedTime,
        phase: currentPhase,
        mode: 'pomodoro',
        completed: true,
      };
      
      completeSession();
      onSessionComplete?.(sessionData);
    }

    // Determine next phase
    const nextPhase = getNextPomodoroPhase(
      currentPhase,
      currentPhase === 'work' ? pomodoroCount + 1 : pomodoroCount,
      longBreakInterval
    );

    onPhaseChange?.(nextPhase);

    // Auto-start next phase if enabled
    const shouldAutoStart = 
      (nextPhase !== 'work' && autoStartBreaks) ||
      (nextPhase === 'work' && autoStartPomodoros);

    if (shouldAutoStart) {
      skipPhase();
      // Timer will continue running
    } else {
      skipPhase();
      pauseTimer();
    }
  }, [
    currentPhase,
    pomodoroCount,
    longBreakInterval,
    vibrationEnabled,
    autoStartBreaks,
    autoStartPomodoros,
    selectedSubject,
    elapsedTime,
    completeSession,
    skipPhase,
    pauseTimer,
    onSessionComplete,
    onPhaseChange,
  ]);

  // Timer interval management
  useEffect(() => {
    if (status === 'running') {
      intervalRef.current = setInterval(tick, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status, tick]);

  // Handle app state changes for background timer
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appStateRef.current === 'background' && nextAppState === 'active') {
        // App came back to foreground
        if (status === 'running') {
          const backgroundDuration = Math.floor((Date.now() - backgroundTimeRef.current) / 1000);
          const newElapsedTime = elapsedTime + backgroundDuration;
          
          const totalDuration = getPhaseDuration(currentPhase, {
            workDuration,
            shortBreakDuration,
            longBreakDuration,
          });

          if (newElapsedTime >= totalDuration) {
            // Phase completed while in background
            handlePhaseComplete();
          } else {
            // Update timer with background time
            updateElapsedTime(newElapsedTime);
            updateDisplayTime(totalDuration - newElapsedTime);
          }
        }
      } else if (nextAppState === 'background') {
        // App went to background
        backgroundTimeRef.current = Date.now();
      }

      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [
    status,
    elapsedTime,
    currentPhase,
    workDuration,
    shortBreakDuration,
    longBreakDuration,
    updateElapsedTime,
    updateDisplayTime,
    handlePhaseComplete,
  ]);

  // Control handlers
  const handleStart = () => {
    startTimer();
  };

  const handlePause = () => {
    pauseTimer();
  };

  const handleStop = () => {
    stopTimer();
  };

  const handleReset = () => {
    resetTimer();
  };

  const handleSkip = () => {
    skipPhase();
  };

  // Get phase display info
  const getPhaseInfo = () => {
    switch (currentPhase) {
      case 'work':
        return {
          title: 'Focus Time',
          subtitle: `Pomodoro ${pomodoroCount + 1}`,
          color: theme.colors.primary,
        };
      case 'shortBreak':
        return {
          title: 'Short Break',
          subtitle: 'Take a quick rest',
          color: theme.colors.secondary,
        };
      case 'longBreak':
        return {
          title: 'Long Break',
          subtitle: 'Well deserved rest!',
          color: theme.colors.tertiary,
        };
      default:
        return {
          title: 'Ready',
          subtitle: 'Start your focus session',
          color: theme.colors.outline,
        };
    }
  };

  const phaseInfo = getPhaseInfo();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    phaseInfo: {
      alignItems: 'center',
      marginBottom: 24,
    },
    phaseTitle: {
      fontSize: 24,
      fontWeight: '600',
      color: phaseInfo.color,
      marginBottom: 4,
    },
    phaseSubtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
    },
    timerCard: {
      marginVertical: 32,
    },
    controls: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
      marginTop: 24,
    },
    primaryButton: {
      minWidth: 120,
    },
    secondaryControls: {
      flexDirection: 'row',
      gap: 8,
      marginTop: 16,
    },
    statsContainer: {
      marginTop: 32,
      padding: 16,
      borderRadius: 12,
      backgroundColor: theme.colors.surfaceVariant,
    },
    statsText: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      {/* Phase Information */}
      <View style={styles.phaseInfo}>
        <Text style={styles.phaseTitle}>{phaseInfo.title}</Text>
        <Text style={styles.phaseSubtitle}>{phaseInfo.subtitle}</Text>
      </View>

      {/* Timer Display */}
      <View style={styles.timerCard}>
        <ExpressiveTimerCard showProgress />
      </View>

      {/* Primary Controls */}
      <View style={styles.controls}>
        {status === 'idle' && (
          <Button
            mode="contained"
            onPress={handleStart}
            style={styles.primaryButton}
            disabled={!selectedSubject}
          >
            Start
          </Button>
        )}
        
        {status === 'running' && (
          <Button
            mode="contained"
            onPress={handlePause}
            style={styles.primaryButton}
          >
            Pause
          </Button>
        )}
        
        {status === 'paused' && (
          <>
            <Button
              mode="contained"
              onPress={handleStart}
              style={styles.primaryButton}
            >
              Resume
            </Button>
            <Button
              mode="outlined"
              onPress={handleStop}
            >
              Stop
            </Button>
          </>
        )}
      </View>

      {/* Secondary Controls */}
      {(status === 'running' || status === 'paused') && (
        <View style={styles.secondaryControls}>
          <IconButton
            icon="skip-next"
            mode="outlined"
            onPress={handleSkip}
            size={20}
          />
          <IconButton
            icon="restart"
            mode="outlined"
            onPress={handleReset}
            size={20}
          />
        </View>
      )}

      {/* Stats */}
      <Surface style={styles.statsContainer}>
        <Text style={styles.statsText}>
          Completed Pomodoros: {pomodoroCount} | 
          Next: {pomodoroCount + 1 === longBreakInterval ? 'Long Break' : 'Short Break'}
        </Text>
      </Surface>
    </View>
  );
};

export default PomodoroTimer;
