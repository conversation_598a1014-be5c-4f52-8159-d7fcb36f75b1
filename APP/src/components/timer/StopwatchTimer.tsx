import React, { useEffect, useRef, useCallback, useState } from 'react';
import {
  View,
  StyleSheet,
  AppState,
  AppStateStatus,
  Vibration,
  Alert,
} from 'react-native';
import { Text, Button, IconButton, Surface, Chip } from 'react-native-paper';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { useTimerStore } from '../../stores/timerStore';
import { formatTime, shouldNotify, getNextNotificationTime } from '../../utils/timerUtils';
import ExpressiveTimerCard from './ExpressiveTimerCard';

interface StopwatchTimerProps {
  onSessionComplete?: (sessionData: any) => void;
  onNotificationInterval?: (interval: number) => void;
}

const StopwatchTimer: React.FC<StopwatchTimerProps> = ({
  onSessionComplete,
  onNotificationInterval,
}) => {
  const theme = useDynamicTheme();
  const {
    status,
    mode,
    displayTime,
    elapsedTime,
    notificationIntervals,
    lastNotificationTime,
    soundEnabled,
    vibrationEnabled,
    selectedSubject,
    currentTaskName,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    updateDisplayTime,
    updateElapsedTime,
    completeSession,
    setMode,
    updateSettings,
  } = useTimerStore();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const backgroundTimeRef = useRef<number>(0);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);
  const [lastNotificationTimeLocal, setLastNotificationTimeLocal] = useState(0);

  // Set mode to stopwatch on mount
  useEffect(() => {
    setMode('stopwatch');
  }, [setMode]);

  // Timer tick logic
  const tick = useCallback(() => {
    if (status === 'running') {
      const newElapsedTime = elapsedTime + 1;
      updateElapsedTime(newElapsedTime);
      updateDisplayTime(newElapsedTime);

      // Check for notification intervals
      if (shouldNotify(newElapsedTime, lastNotificationTimeLocal, notificationIntervals)) {
        const currentMinute = Math.floor(newElapsedTime / 60);
        const intervalHit = notificationIntervals.find(interval => 
          currentMinute === interval && Math.floor(lastNotificationTimeLocal / 60) < interval
        );

        if (intervalHit) {
          handleNotificationInterval(intervalHit);
          setLastNotificationTimeLocal(newElapsedTime);
        }
      }
    }
  }, [
    status,
    elapsedTime,
    notificationIntervals,
    lastNotificationTimeLocal,
    updateElapsedTime,
    updateDisplayTime,
  ]);

  // Handle notification interval
  const handleNotificationInterval = useCallback((interval: number) => {
    // Vibration feedback
    if (vibrationEnabled) {
      Vibration.vibrate([0, 200, 100, 200]);
    }

    onNotificationInterval?.(interval);

    // Show alert for interval
    Alert.alert(
      'Study Interval',
      `You've been studying for ${interval} minutes! Keep going!`,
      [{ text: 'OK', style: 'default' }]
    );
  }, [vibrationEnabled, onNotificationInterval]);

  // Timer interval management
  useEffect(() => {
    if (status === 'running') {
      intervalRef.current = setInterval(tick, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status, tick]);

  // Handle app state changes for background timer
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appStateRef.current === 'background' && nextAppState === 'active') {
        // App came back to foreground
        if (status === 'running') {
          const backgroundDuration = Math.floor((Date.now() - backgroundTimeRef.current) / 1000);
          const newElapsedTime = elapsedTime + backgroundDuration;
          
          updateElapsedTime(newElapsedTime);
          updateDisplayTime(newElapsedTime);

          // Check if any notification intervals were missed
          const missedIntervals = notificationIntervals.filter(interval => {
            const intervalSeconds = interval * 60;
            return intervalSeconds > lastNotificationTimeLocal && 
                   intervalSeconds <= newElapsedTime;
          });

          if (missedIntervals.length > 0) {
            const latestInterval = Math.max(...missedIntervals);
            handleNotificationInterval(latestInterval);
            setLastNotificationTimeLocal(newElapsedTime);
          }
        }
      } else if (nextAppState === 'background') {
        // App went to background
        backgroundTimeRef.current = Date.now();
      }

      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [
    status,
    elapsedTime,
    notificationIntervals,
    lastNotificationTimeLocal,
    updateElapsedTime,
    updateDisplayTime,
    handleNotificationInterval,
  ]);

  // Control handlers
  const handleStart = () => {
    startTimer();
  };

  const handlePause = () => {
    pauseTimer();
  };

  const handleStop = () => {
    if (elapsedTime > 0) {
      Alert.alert(
        'Complete Session',
        'Would you like to save this study session?',
        [
          {
            text: 'Discard',
            style: 'destructive',
            onPress: () => {
              stopTimer();
              setLastNotificationTimeLocal(0);
            },
          },
          {
            text: 'Save Session',
            style: 'default',
            onPress: () => {
              const sessionData = {
                subject: selectedSubject || 'Unknown',
                taskName: currentTaskName,
                duration: elapsedTime,
                mode: 'stopwatch',
                completed: true,
              };
              
              completeSession();
              onSessionComplete?.(sessionData);
              stopTimer();
              setLastNotificationTimeLocal(0);
            },
          },
        ]
      );
    } else {
      stopTimer();
      setLastNotificationTimeLocal(0);
    }
  };

  const handleReset = () => {
    Alert.alert(
      'Reset Timer',
      'Are you sure you want to reset the timer? All progress will be lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            resetTimer();
            setLastNotificationTimeLocal(0);
          },
        },
      ]
    );
  };

  // Get next notification info
  const getNextNotification = () => {
    const nextTime = getNextNotificationTime(elapsedTime, notificationIntervals);
    if (nextTime) {
      const minutes = Math.floor(nextTime / 60);
      return `Next notification: ${minutes}min`;
    }
    return 'No more notifications';
  };

  // Update notification intervals
  const updateNotificationIntervals = (intervals: number[]) => {
    updateSettings({ notificationIntervals: intervals });
  };

  // Preset interval options
  const presetIntervals = [
    { label: 'Every 15min', intervals: [15, 30, 45, 60, 75, 90] },
    { label: 'Every 30min', intervals: [30, 60, 90, 120] },
    { label: 'Every 45min', intervals: [45, 90, 135, 180] },
    { label: 'Hourly', intervals: [60, 120, 180, 240] },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    header: {
      alignItems: 'center',
      marginBottom: 24,
    },
    title: {
      fontSize: 24,
      fontWeight: '600',
      color: theme.colors.primary,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
    },
    timerCard: {
      marginVertical: 32,
    },
    controls: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
      marginTop: 24,
    },
    primaryButton: {
      minWidth: 120,
    },
    secondaryControls: {
      flexDirection: 'row',
      gap: 8,
      marginTop: 16,
    },
    notificationInfo: {
      marginTop: 32,
      padding: 16,
      borderRadius: 12,
      backgroundColor: theme.colors.surfaceVariant,
      alignItems: 'center',
    },
    notificationText: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 12,
    },
    intervalPresets: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      justifyContent: 'center',
    },
    presetChip: {
      marginVertical: 2,
    },
    sessionInfo: {
      marginTop: 16,
      padding: 12,
      borderRadius: 8,
      backgroundColor: theme.colors.primaryContainer,
    },
    sessionText: {
      fontSize: 12,
      color: theme.colors.onPrimaryContainer,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Stopwatch Mode</Text>
        <Text style={styles.subtitle}>Study at your own pace</Text>
      </View>

      {/* Timer Display */}
      <View style={styles.timerCard}>
        <ExpressiveTimerCard showProgress={false} />
      </View>

      {/* Primary Controls */}
      <View style={styles.controls}>
        {status === 'idle' && (
          <Button
            mode="contained"
            onPress={handleStart}
            style={styles.primaryButton}
            disabled={!selectedSubject}
          >
            Start
          </Button>
        )}
        
        {status === 'running' && (
          <Button
            mode="contained"
            onPress={handlePause}
            style={styles.primaryButton}
          >
            Pause
          </Button>
        )}
        
        {status === 'paused' && (
          <>
            <Button
              mode="contained"
              onPress={handleStart}
              style={styles.primaryButton}
            >
              Resume
            </Button>
            <Button
              mode="outlined"
              onPress={handleStop}
            >
              Stop
            </Button>
          </>
        )}
      </View>

      {/* Secondary Controls */}
      {(status === 'running' || status === 'paused') && (
        <View style={styles.secondaryControls}>
          <IconButton
            icon="restart"
            mode="outlined"
            onPress={handleReset}
            size={20}
          />
        </View>
      )}

      {/* Notification Settings */}
      <Surface style={styles.notificationInfo}>
        <Text style={styles.notificationText}>
          {status === 'running' ? getNextNotification() : 'Notification Intervals'}
        </Text>
        
        {status === 'idle' && (
          <View style={styles.intervalPresets}>
            {presetIntervals.map((preset, index) => (
              <Chip
                key={index}
                mode="outlined"
                selected={JSON.stringify(notificationIntervals) === JSON.stringify(preset.intervals)}
                onPress={() => updateNotificationIntervals(preset.intervals)}
                style={styles.presetChip}
              >
                {preset.label}
              </Chip>
            ))}
          </View>
        )}

        {/* Current session info */}
        {(status === 'running' || status === 'paused') && selectedSubject && (
          <Surface style={styles.sessionInfo}>
            <Text style={styles.sessionText}>
              Studying: {selectedSubject}
              {currentTaskName && ` - ${currentTaskName}`}
            </Text>
          </Surface>
        )}
      </Surface>
    </View>
  );
};

export default StopwatchTimer;
