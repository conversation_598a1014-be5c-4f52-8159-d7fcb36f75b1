import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Pressable,
  Dimensions,
} from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  Button,
  TextInput,
  Modal,
  Portal,
} from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { useTimerStore } from '../../stores/timerStore';

const { width: screenWidth } = Dimensions.get('window');

interface Subject {
  id: string;
  name: string;
  color: string;
  totalTime?: number;
  sessionsToday?: number;
}

interface SubjectSelectorProps {
  subjects: Subject[];
  onSubjectSelect: (subject: Subject) => void;
  onAddSubject: (name: string, color: string) => void;
  compact?: boolean;
  showStats?: boolean;
}

const SubjectSelector: React.FC<SubjectSelectorProps> = ({
  subjects,
  onSubjectSelect,
  onAddSubject,
  compact = false,
  showStats = true,
}) => {
  const theme = useDynamicTheme();
  const { selectedSubject, setSubject } = useTimerStore();
  
  const [showAddModal, setShowAddModal] = useState(false);
  const [newSubjectName, setNewSubjectName] = useState('');
  const [selectedColor, setSelectedColor] = useState('#6750A4');

  // Animation values
  const addButtonScale = useSharedValue(1);
  const modalScale = useSharedValue(0);

  // Predefined colors
  const predefinedColors = [
    '#6750A4', '#7C4DFF', '#3F51B5', '#2196F3',
    '#00BCD4', '#009688', '#4CAF50', '#8BC34A',
    '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800',
    '#FF5722', '#F44336', '#E91E63', '#9C27B0',
  ];

  // Handle subject selection
  const handleSubjectSelect = (subject: Subject) => {
    setSubject(subject.name);
    onSubjectSelect(subject);
  };

  // Handle add subject
  const handleAddSubject = () => {
    if (newSubjectName.trim()) {
      onAddSubject(newSubjectName.trim(), selectedColor);
      setNewSubjectName('');
      setShowAddModal(false);
    }
  };

  // Animation for add button
  const addButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: addButtonScale.value }],
  }));

  // Animation for modal
  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
    opacity: interpolate(modalScale.value, [0, 1], [0, 1]),
  }));

  // Modal animations
  useEffect(() => {
    if (showAddModal) {
      modalScale.value = withSpring(1, { damping: 15 });
    } else {
      modalScale.value = withTiming(0, { duration: 200 });
    }
  }, [showAddModal]);

  // Subject card component
  const SubjectCard = ({ subject }: { subject: Subject }) => {
    const isSelected = selectedSubject === subject.name;
    const cardScale = useSharedValue(1);

    const cardAnimatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: cardScale.value }],
    }));

    const handlePress = () => {
      cardScale.value = withSpring(0.95, { damping: 15 }, () => {
        cardScale.value = withSpring(1, { damping: 15 });
      });
      handleSubjectSelect(subject);
    };

    return (
      <Animated.View style={cardAnimatedStyle}>
        <Pressable onPress={handlePress}>
          <Surface
            style={[
              styles.subjectCard,
              compact && styles.subjectCardCompact,
              {
                backgroundColor: isSelected 
                  ? `${subject.color}20` 
                  : theme.colors.surface,
                borderColor: isSelected 
                  ? subject.color 
                  : theme.colors.outline,
                borderWidth: isSelected ? 2 : 1,
              },
            ]}
            elevation={isSelected ? 4 : 1}
          >
            {/* Color indicator */}
            <View
              style={[
                styles.colorIndicator,
                compact && styles.colorIndicatorCompact,
                { backgroundColor: subject.color },
              ]}
            />

            {/* Subject info */}
            <View style={styles.subjectInfo}>
              <Text
                style={[
                  styles.subjectName,
                  compact && styles.subjectNameCompact,
                  { color: isSelected ? subject.color : theme.colors.onSurface },
                ]}
                numberOfLines={1}
              >
                {subject.name}
              </Text>

              {showStats && !compact && (
                <View style={styles.subjectStats}>
                  {subject.totalTime !== undefined && (
                    <Text style={styles.statText}>
                      {Math.floor(subject.totalTime / 3600)}h {Math.floor((subject.totalTime % 3600) / 60)}m today
                    </Text>
                  )}
                  {subject.sessionsToday !== undefined && (
                    <Text style={styles.statText}>
                      {subject.sessionsToday} sessions
                    </Text>
                  )}
                </View>
              )}
            </View>

            {/* Selection indicator */}
            {isSelected && (
              <IconButton
                icon="check"
                size={compact ? 16 : 20}
                iconColor={subject.color}
                style={styles.selectionIndicator}
              />
            )}
          </Surface>
        </Pressable>
      </Animated.View>
    );
  };

  // Color picker component
  const ColorPicker = () => (
    <View style={styles.colorPicker}>
      <Text style={styles.colorPickerLabel}>Choose a color</Text>
      <View style={styles.colorGrid}>
        {predefinedColors.map((color) => (
          <Pressable
            key={color}
            style={[
              styles.colorOption,
              { backgroundColor: color },
              selectedColor === color && styles.selectedColorOption,
            ]}
            onPress={() => setSelectedColor(color)}
          >
            {selectedColor === color && (
              <IconButton
                icon="check"
                size={16}
                iconColor="white"
                style={{ margin: 0 }}
              />
            )}
          </Pressable>
        ))}
      </View>
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      padding: compact ? 12 : 16,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: compact ? 12 : 16,
    },
    title: {
      fontSize: compact ? 16 : 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
    },
    addButton: {
      backgroundColor: theme.colors.primaryContainer,
    },
    subjectsGrid: {
      gap: compact ? 8 : 12,
    },
    subjectCard: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: compact ? 12 : 16,
      borderRadius: compact ? 12 : 16,
      marginBottom: compact ? 8 : 12,
    },
    subjectCardCompact: {
      padding: 8,
      borderRadius: 8,
    },
    colorIndicator: {
      width: compact ? 16 : 20,
      height: compact ? 16 : 20,
      borderRadius: compact ? 8 : 10,
      marginRight: compact ? 8 : 12,
    },
    colorIndicatorCompact: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: 6,
    },
    subjectInfo: {
      flex: 1,
    },
    subjectName: {
      fontSize: compact ? 14 : 16,
      fontWeight: '500',
    },
    subjectNameCompact: {
      fontSize: 12,
    },
    subjectStats: {
      marginTop: 4,
      gap: 2,
    },
    statText: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    selectionIndicator: {
      margin: 0,
    },
    emptyState: {
      alignItems: 'center',
      padding: 32,
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 16,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    modal: {
      width: Math.min(screenWidth - 40, 400),
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      elevation: 24,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 20,
      textAlign: 'center',
    },
    input: {
      marginBottom: 20,
    },
    colorPicker: {
      marginBottom: 20,
    },
    colorPickerLabel: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    colorOption: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 2,
    },
    selectedColorOption: {
      elevation: 4,
      transform: [{ scale: 1.1 }],
    },
    modalActions: {
      flexDirection: 'row',
      gap: 12,
    },
    modalButton: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Select Subject</Text>
        <Animated.View style={addButtonAnimatedStyle}>
          <IconButton
            icon="plus"
            mode="contained"
            size={compact ? 20 : 24}
            style={styles.addButton}
            onPress={() => {
              addButtonScale.value = withSpring(0.9, { damping: 15 }, () => {
                addButtonScale.value = withSpring(1, { damping: 15 });
              });
              setShowAddModal(true);
            }}
          />
        </Animated.View>
      </View>

      {/* Subjects list */}
      {subjects.length > 0 ? (
        <ScrollView 
          style={styles.subjectsGrid}
          showsVerticalScrollIndicator={false}
        >
          {subjects.map((subject) => (
            <SubjectCard key={subject.id} subject={subject} />
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            No subjects yet.{'\n'}Add your first subject to get started!
          </Text>
          <Button
            mode="contained"
            onPress={() => setShowAddModal(true)}
            icon="plus"
          >
            Add Subject
          </Button>
        </View>
      )}

      {/* Add Subject Modal */}
      <Portal>
        <Modal
          visible={showAddModal}
          onDismiss={() => setShowAddModal(false)}
          contentContainerStyle={styles.modalOverlay}
        >
          <Animated.View style={[styles.modal, modalAnimatedStyle]}>
            <Text style={styles.modalTitle}>Add New Subject</Text>
            
            <TextInput
              label="Subject Name"
              value={newSubjectName}
              onChangeText={setNewSubjectName}
              mode="outlined"
              style={styles.input}
              placeholder="e.g., Mathematics, History, Programming"
            />

            <ColorPicker />

            <View style={styles.modalActions}>
              <Button
                mode="outlined"
                onPress={() => setShowAddModal(false)}
                style={styles.modalButton}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleAddSubject}
                style={styles.modalButton}
                disabled={!newSubjectName.trim()}
              >
                Add Subject
              </Button>
            </View>
          </Animated.View>
        </Modal>
      </Portal>
    </View>
  );
};

export default SubjectSelector;
