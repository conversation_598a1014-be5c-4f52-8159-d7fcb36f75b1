// ===========================================
// 🎨 ICON SYSTEM CONSTANTS
// ===========================================

// Icon sizes following Material 3 specification
export const IconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  xxl: 40,
  xxxl: 48,
} as const;

export type IconSize = keyof typeof IconSizes;

// Material Design Icon names (commonly used)
export const MaterialIcons = {
  // Navigation
  home: 'home',
  homeOutlined: 'home-outline',
  back: 'arrow-back',
  forward: 'arrow-forward',
  up: 'arrow-upward',
  down: 'arrow-downward',
  menu: 'menu',
  close: 'close',
  search: 'search',
  
  // Timer & Productivity
  timer: 'timer',
  timerOff: 'timer-off',
  play: 'play-arrow',
  pause: 'pause',
  stop: 'stop',
  refresh: 'refresh',
  schedule: 'schedule',
  alarm: 'alarm',
  alarmOn: 'alarm-on',
  alarmOff: 'alarm-off',
  
  // Tasks & Organization
  task: 'task',
  taskAlt: 'task-alt',
  checklist: 'checklist',
  checklistRtl: 'checklist-rtl',
  check: 'check',
  checkCircle: 'check-circle',
  checkCircleOutline: 'check-circle-outline',
  add: 'add',
  addCircle: 'add-circle',
  addCircleOutline: 'add-circle-outline',
  remove: 'remove',
  removeCircle: 'remove-circle',
  edit: 'edit',
  delete: 'delete',
  deleteOutline: 'delete-outline',
  
  // Analytics & Charts
  analytics: 'analytics',
  barChart: 'bar-chart',
  pieChart: 'pie-chart',
  lineChart: 'show-chart',
  trending: 'trending-up',
  trendingDown: 'trending-down',
  insights: 'insights',
  
  // User & Profile
  person: 'person',
  personOutline: 'person-outline',
  people: 'people',
  peopleOutline: 'people-outline',
  account: 'account-circle',
  accountOutline: 'account-circle-outline',
  
  // Settings & Configuration
  settings: 'settings',
  settingsOutline: 'settings-outline',
  tune: 'tune',
  palette: 'palette',
  colorLens: 'color-lens',
  brightness: 'brightness-6',
  darkMode: 'dark-mode',
  lightMode: 'light-mode',
  
  // Communication & Social
  notification: 'notifications',
  notificationOutline: 'notifications-outline',
  notificationOff: 'notifications-off',
  message: 'message',
  messageOutline: 'message-outline',
  chat: 'chat',
  chatOutline: 'chat-outline',
  share: 'share',
  
  // Files & Documents
  folder: 'folder',
  folderOutline: 'folder-outline',
  file: 'description',
  fileOutline: 'description-outline',
  upload: 'upload',
  download: 'download',
  attachment: 'attachment',
  
  // Status & Feedback
  info: 'info',
  infoOutline: 'info-outline',
  warning: 'warning',
  warningOutline: 'warning-outline',
  error: 'error',
  errorOutline: 'error-outline',
  success: 'check-circle',
  help: 'help',
  helpOutline: 'help-outline',
  
  // Actions
  favorite: 'favorite',
  favoriteOutline: 'favorite-outline',
  bookmark: 'bookmark',
  bookmarkOutline: 'bookmark-outline',
  star: 'star',
  starOutline: 'star-outline',
  thumbUp: 'thumb-up',
  thumbDown: 'thumb-down',
  
  // Media & Content
  image: 'image',
  imageOutline: 'image-outline',
  video: 'videocam',
  videoOutline: 'videocam-outline',
  audio: 'audiotrack',
  volume: 'volume-up',
  volumeOff: 'volume-off',
  
  // Connectivity
  wifi: 'wifi',
  wifiOff: 'wifi-off',
  bluetooth: 'bluetooth',
  bluetoothOff: 'bluetooth-disabled',
  sync: 'sync',
  syncDisabled: 'sync-disabled',
  cloud: 'cloud',
  cloudOff: 'cloud-off',
  
  // Miscellaneous
  more: 'more-vert',
  moreHoriz: 'more-horiz',
  expand: 'expand-more',
  collapse: 'expand-less',
  fullscreen: 'fullscreen',
  fullscreenExit: 'fullscreen-exit',
  visibility: 'visibility',
  visibilityOff: 'visibility-off',
  lock: 'lock',
  lockOpen: 'lock-open',
  key: 'key',
  
  // Directional
  chevronLeft: 'chevron-left',
  chevronRight: 'chevron-right',
  chevronUp: 'keyboard-arrow-up',
  chevronDown: 'keyboard-arrow-down',
  
  // Special
  grade: 'grade',
  workspace: 'workspace-premium',
  extension: 'extension',
  dashboard: 'dashboard',
  widgets: 'widgets',
} as const;

// Emoji icons for expressive design
export const EmojiIcons = {
  // Timer & Focus
  pomodoro: '🍅',
  timer: '⏱️',
  stopwatch: '⏲️',
  clock: '🕐',
  alarm: '⏰',
  hourglass: '⏳',
  
  // Emotions & States
  focus: '🎯',
  energy: '⚡',
  calm: '🧘',
  creativity: '🎨',
  success: '✅',
  celebration: '🎉',
  fire: '🔥',
  star: '⭐',
  sparkles: '✨',
  
  // Activities
  study: '📚',
  reading: '📖',
  writing: '✍️',
  thinking: '🤔',
  lightbulb: '💡',
  brain: '🧠',
  
  // Progress & Achievement
  trophy: '🏆',
  medal: '🏅',
  target: '🎯',
  rocket: '🚀',
  mountain: '⛰️',
  flag: '🏁',
  
  // Time & Calendar
  calendar: '📅',
  date: '📆',
  schedule: '🗓️',
  
  // Communication
  bell: '🔔',
  megaphone: '📢',
  speech: '💬',
  message: '💌',
  
  // Tools & Objects
  gear: '⚙️',
  wrench: '🔧',
  hammer: '🔨',
  key: '🔑',
  lock: '🔒',
  unlock: '🔓',
  
  // Nature & Weather
  sun: '☀️',
  moon: '🌙',
  cloud: '☁️',
  rainbow: '🌈',
  tree: '🌳',
  flower: '🌸',
  
  // Food & Drinks
  coffee: '☕',
  tea: '🍵',
  water: '💧',
  apple: '🍎',
  
  // Gestures & Actions
  thumbsUp: '👍',
  thumbsDown: '👎',
  clap: '👏',
  wave: '👋',
  point: '👉',
  
  // Faces & Expressions
  smile: '😊',
  happy: '😄',
  love: '😍',
  cool: '😎',
  thinking: '🤔',
  sleepy: '😴',
  
  // Symbols
  check: '✓',
  cross: '✗',
  plus: '➕',
  minus: '➖',
  arrow: '➡️',
  arrowUp: '⬆️',
  arrowDown: '⬇️',
  
  // Numbers (for badges, counters)
  zero: '0️⃣',
  one: '1️⃣',
  two: '2️⃣',
  three: '3️⃣',
  four: '4️⃣',
  five: '5️⃣',
  six: '6️⃣',
  seven: '7️⃣',
  eight: '8️⃣',
  nine: '9️⃣',
  ten: '🔟',
} as const;

// Icon families/types
export type IconFamily = 'material' | 'emoji' | 'custom';

// Icon configuration
export interface IconConfig {
  family: IconFamily;
  name: string;
  size?: IconSize | number;
  color?: string;
  style?: any;
}

// Predefined icon sets for common use cases
export const IconSets = {
  navigation: {
    home: { family: 'material' as const, name: MaterialIcons.home },
    timer: { family: 'material' as const, name: MaterialIcons.timer },
    tasks: { family: 'material' as const, name: MaterialIcons.task },
    analytics: { family: 'material' as const, name: MaterialIcons.analytics },
    profile: { family: 'material' as const, name: MaterialIcons.person },
  },
  
  timer: {
    play: { family: 'material' as const, name: MaterialIcons.play },
    pause: { family: 'material' as const, name: MaterialIcons.pause },
    stop: { family: 'material' as const, name: MaterialIcons.stop },
    reset: { family: 'material' as const, name: MaterialIcons.refresh },
    pomodoro: { family: 'emoji' as const, name: EmojiIcons.pomodoro },
    focus: { family: 'emoji' as const, name: EmojiIcons.focus },
  },
  
  status: {
    success: { family: 'emoji' as const, name: EmojiIcons.success },
    error: { family: 'material' as const, name: MaterialIcons.error },
    warning: { family: 'material' as const, name: MaterialIcons.warning },
    info: { family: 'material' as const, name: MaterialIcons.info },
  },
  
  actions: {
    add: { family: 'material' as const, name: MaterialIcons.add },
    edit: { family: 'material' as const, name: MaterialIcons.edit },
    delete: { family: 'material' as const, name: MaterialIcons.delete },
    share: { family: 'material' as const, name: MaterialIcons.share },
    favorite: { family: 'material' as const, name: MaterialIcons.favorite },
  },
} as const;

// Utility functions
export const getIconSize = (size: IconSize | number): number => {
  return typeof size === 'number' ? size : IconSizes[size];
};

export const getIconConfig = (iconSet: keyof typeof IconSets, iconName: string): IconConfig | undefined => {
  const set = IconSets[iconSet];
  return set?.[iconName as keyof typeof set];
};
