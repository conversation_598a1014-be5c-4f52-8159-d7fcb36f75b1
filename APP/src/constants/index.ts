// Constants exports
// This file will be populated as constants are created

// Re-export all expressive theme constants including typography
export * from './expressiveTheme';

// Typography-specific exports for easier access
export {
  ExpressiveTypography,
  ExpressiveTypographyVariants,
  EmotionalTypography,
  ExpressiveFontWeights,
  ExpressiveFontFamilies,
  getTypographyStyle,
  getTypographyVariant,
  getEmotionalTypography,
  getFontWeight,
  getFontFamily,
} from './expressiveTheme';

// Dynamic color system exports
export * from './dynamicColors';
export {
  FallbackSeedColors,
  defaultDynamicColorConfig,
  getMaterialYouPalette,
  generatePaletteFromSeedColor,
  mapMaterialYouToTheme,
  isMaterialYouSupported,
  getContextualGenerationStyle,
  MaterialYou,
} from './dynamicColors';

// Motion system exports
export * from './expressiveMotion';
export {
  ExpressiveEasing,
  ExpressiveDurations,
  ExpressiveSprings,
  ExpressiveDelays,
  ExpressiveOrigins,
  ExpressiveAnimations,
  EmotionalMotion,
  GestureMotion,
  LoadingMotion,
  PageTransitions,
  MotionUtils,
} from './expressiveMotion';

// Surface elevation system exports
export * from './surfaceElevation';
export {
  ExpressiveElevation,
  ColoredElevation,
  SurfaceContainers,
  BlurEffects,
  getElevationStyle,
  getAnimatedElevationStyle,
  getSurfaceContainerColor,
  getElevationWithRadius,
  getContextualElevation,
  getSurfaceElevation,
} from './surfaceElevation';

// Icon system exports
export * from './icons';
export {
  IconSizes,
  MaterialIcons,
  EmojiIcons,
  IconSets,
  getIconSize,
  getIconConfig,
} from './icons';

// Placeholder to prevent TypeScript errors
export {};
