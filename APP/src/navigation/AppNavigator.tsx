import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../constants/expressiveTheme';
import ExpressiveIcon from '../components/icons/ExpressiveIcon';
import { MaterialIcons } from '../constants/icons';

// Screens
import HomeScreen from '../screens/HomeScreen';
import TimerScreen from '../screens/TimerScreen';
import AnalyticsScreen from '../screens/AnalyticsScreen';
import TasksScreen from '../screens/TasksScreen';
import ProfileScreen from '../screens/ProfileScreen';

// Auth Screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';

// ===========================================
// 🧭 APP NAVIGATOR
// ===========================================

// Navigation types
export type RootStackParamList = {
  Main: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Timer: undefined;
  Tasks: undefined;
  Analytics: undefined;
  Profile: undefined;
};

const RootStack = createNativeStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

// Main Tab Navigator
const MainTabNavigator: React.FC = () => {
  const theme = useDynamicTheme();

  return (
    <MainTab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
          borderTopWidth: 1,
          paddingBottom: 8,
          paddingTop: 8,
          height: 80,
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarLabelStyle: {
          ...ExpressiveTypographyVariants.navigationLabel,
          fontSize: 12,
          fontWeight: '600',
        },
      }}
    >
      <MainTab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ExpressiveIcon
              family="material"
              name={focused ? MaterialIcons.home : MaterialIcons.homeOutlined}
              size="lg"
              color={color}
            />
          ),
        }}
      />
      <MainTab.Screen
        name="Timer"
        component={TimerScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ExpressiveIcon
              family="material"
              name={MaterialIcons.timer}
              size="lg"
              color={color}
            />
          ),
        }}
      />
      <MainTab.Screen
        name="Tasks"
        component={TasksScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ExpressiveIcon
              family="material"
              name={MaterialIcons.task}
              size="lg"
              color={color}
            />
          ),
        }}
      />
      <MainTab.Screen
        name="Analytics"
        component={AnalyticsScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ExpressiveIcon
              family="material"
              name={MaterialIcons.analytics}
              size="lg"
              color={color}
            />
          ),
        }}
      />
      <MainTab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ExpressiveIcon
              family="material"
              name={focused ? MaterialIcons.person : MaterialIcons.personOutline}
              size="lg"
              color={color}
            />
          ),
        }}
      />
    </MainTab.Navigator>
  );
};

// Root Stack Navigator
const RootStackNavigator: React.FC = () => {
  const theme = useDynamicTheme();
  
  // TODO: Replace with actual auth state
  const isAuthenticated = true;

  return (
    <RootStack.Navigator
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: theme.colors.background },
        animation: 'slide_from_right',
        animationDuration: 300,
      }}
    >
      {isAuthenticated ? (
        <RootStack.Screen name="Main" component={MainTabNavigator} />
      ) : (
        <>
          <RootStack.Screen name="Login" component={LoginScreen} />
          <RootStack.Screen name="Register" component={RegisterScreen} />
          <RootStack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
        </>
      )}
    </RootStack.Navigator>
  );
};

// Main App Navigator
export const AppNavigator: React.FC = () => {
  const theme = useDynamicTheme();

  return (
    <NavigationContainer
      theme={{
        dark: theme.isDark,
        colors: {
          primary: theme.colors.primary,
          background: theme.colors.background,
          card: theme.colors.surface,
          text: theme.colors.onSurface,
          border: theme.colors.outline,
          notification: theme.colors.error,
        },
      }}
    >
      <RootStackNavigator />
    </NavigationContainer>
  );
};

export default AppNavigator;
