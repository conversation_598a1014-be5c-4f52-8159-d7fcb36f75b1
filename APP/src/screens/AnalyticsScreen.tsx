import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import ExpressiveCard from '../components/surfaces/ExpressiveCard';

// ===========================================
// 📊 ANALYTICS SCREEN
// ===========================================

export const AnalyticsScreen: React.FC = () => {
  const theme = useDynamicTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
    },
    header: {
      marginBottom: 24,
    },
    title: {
      ...ExpressiveTypography.displaySmall,
      color: theme.colors.onBackground,
      marginBottom: 8,
    },
    subtitle: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      lineHeight: 24,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onBackground,
      marginBottom: 16,
    },
    cardGrid: {
      gap: 16,
    },
    statsRow: {
      flexDirection: 'row',
      gap: 12,
    },
    statCard: {
      flex: 1,
    },
    chartPlaceholder: {
      height: 200,
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 16,
    },
    chartText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Analytics</Text>
          <Text style={styles.subtitle}>
            Track your productivity and see how you're improving over time.
          </Text>
        </View>

        {/* Overview Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>This Week</Text>
          <View style={styles.statsRow}>
            <ExpressiveCard
              title="15h 30m"
              subtitle="Total Focus Time"
              variant="filled"
              colorVariant="primary"
              style={styles.statCard}
            />
            <ExpressiveCard
              title="42"
              subtitle="Sessions Completed"
              variant="filled"
              colorVariant="secondary"
              style={styles.statCard}
            />
          </View>
          
          <View style={[styles.statsRow, { marginTop: 12 }]}>
            <ExpressiveCard
              title="85%"
              subtitle="Goal Achievement"
              variant="filled"
              colorVariant="success"
              style={styles.statCard}
            />
            <ExpressiveCard
              title="7"
              subtitle="Day Streak"
              variant="filled"
              colorVariant="tertiary"
              style={styles.statCard}
            />
          </View>
        </View>

        {/* Time Distribution */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Time Distribution</Text>
          <ExpressiveCard
            title="Subject Breakdown"
            subtitle="How you spent your focus time"
            variant="elevated"
            elevation={2}
          >
            <View style={styles.chartPlaceholder}>
              <Text style={styles.chartText}>📊 Chart will be implemented here</Text>
            </View>
          </ExpressiveCard>
        </View>

        {/* Productivity Trends */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Productivity Trends</Text>
          <ExpressiveCard
            title="Daily Progress"
            subtitle="Your focus time over the past 7 days"
            variant="elevated"
            elevation={2}
          >
            <View style={styles.chartPlaceholder}>
              <Text style={styles.chartText}>📈 Trend chart will be implemented here</Text>
            </View>
          </ExpressiveCard>
        </View>

        {/* Achievements */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Achievements</Text>
          <View style={styles.cardGrid}>
            <ExpressiveCard
              title="🔥 Week Warrior"
              subtitle="Completed 7 days in a row"
              description="Keep up the amazing consistency!"
              variant="outlined"
              colorVariant="warning"
            />
            
            <ExpressiveCard
              title="🎯 Focus Master"
              subtitle="Completed 50 Pomodoro sessions"
              description="You're becoming a productivity expert!"
              variant="outlined"
              colorVariant="success"
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default AnalyticsScreen;
