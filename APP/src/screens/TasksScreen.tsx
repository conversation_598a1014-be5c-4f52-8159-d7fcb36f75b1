import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import ExpressiveCard from '../components/surfaces/ExpressiveCard';
import ExpressiveButton from '../components/buttons/ExpressiveButton';
import ExpressiveFAB from '../components/buttons/ExpressiveFAB';

// ===========================================
// 📋 TASKS SCREEN
// ===========================================

export const TasksScreen: React.FC = () => {
  const theme = useDynamicTheme();
  const [viewMode, setViewMode] = useState<'kanban' | 'list'>('list');

  // Mock task data
  const tasks = [
    {
      id: '1',
      title: 'Complete React Native app',
      description: 'Finish the timer and analytics features',
      status: 'in-progress',
      priority: 'high',
      dueDate: 'Today',
    },
    {
      id: '2',
      title: 'Study for mathematics exam',
      description: 'Review calculus chapters 1-5',
      status: 'todo',
      priority: 'medium',
      dueDate: 'Tomorrow',
    },
    {
      id: '3',
      title: 'Write project documentation',
      description: 'Document the API endpoints and usage',
      status: 'todo',
      priority: 'low',
      dueDate: 'Next week',
    },
    {
      id: '4',
      title: 'Team meeting preparation',
      description: 'Prepare slides for the weekly standup',
      status: 'completed',
      priority: 'medium',
      dueDate: 'Yesterday',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      default:
        return 'primary';
    }
  };

  const getPriorityEmoji = (priority: string) => {
    switch (priority) {
      case 'high':
        return '🔴';
      case 'medium':
        return '🟡';
      case 'low':
        return '🟢';
      default:
        return '⚪';
    }
  };

  const getStatusEmoji = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'in-progress':
        return '🔄';
      default:
        return '📝';
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
      paddingBottom: 100, // Space for FAB
    },
    header: {
      marginBottom: 24,
    },
    title: {
      ...ExpressiveTypography.displaySmall,
      color: theme.colors.onBackground,
      marginBottom: 8,
    },
    subtitle: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      lineHeight: 24,
    },
    controls: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 24,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onBackground,
      marginBottom: 16,
    },
    taskList: {
      gap: 12,
    },
    taskMeta: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginTop: 8,
    },
    metaText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    statsRow: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 16,
    },
    statCard: {
      flex: 1,
    },
  });

  const todoTasks = tasks.filter(task => task.status === 'todo');
  const inProgressTasks = tasks.filter(task => task.status === 'in-progress');
  const completedTasks = tasks.filter(task => task.status === 'completed');

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Tasks</Text>
          <Text style={styles.subtitle}>
            Organize your work and track your progress with our task management system.
          </Text>
        </View>

        {/* Controls */}
        <View style={styles.controls}>
          <ExpressiveButton
            title="List View"
            variant={viewMode === 'list' ? 'filled' : 'outlined'}
            size="small"
            onPress={() => setViewMode('list')}
          />
          <ExpressiveButton
            title="Kanban View"
            variant={viewMode === 'kanban' ? 'filled' : 'outlined'}
            size="small"
            onPress={() => setViewMode('kanban')}
          />
        </View>

        {/* Task Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.statsRow}>
            <ExpressiveCard
              title={todoTasks.length.toString()}
              subtitle="To Do"
              variant="filled"
              colorVariant="primary"
              style={styles.statCard}
            />
            <ExpressiveCard
              title={inProgressTasks.length.toString()}
              subtitle="In Progress"
              variant="filled"
              colorVariant="warning"
              style={styles.statCard}
            />
            <ExpressiveCard
              title={completedTasks.length.toString()}
              subtitle="Completed"
              variant="filled"
              colorVariant="success"
              style={styles.statCard}
            />
          </View>
        </View>

        {/* Task Lists */}
        {viewMode === 'list' ? (
          <>
            {/* In Progress Tasks */}
            {inProgressTasks.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>In Progress</Text>
                <View style={styles.taskList}>
                  {inProgressTasks.map((task) => (
                    <ExpressiveCard
                      key={task.id}
                      title={task.title}
                      description={task.description}
                      variant="elevated"
                      elevation={2}
                      colorVariant={getStatusColor(task.status) as any}
                    >
                      <View style={styles.taskMeta}>
                        <Text style={styles.metaText}>
                          {getStatusEmoji(task.status)} {task.status.replace('-', ' ')}
                        </Text>
                        <Text style={styles.metaText}>
                          {getPriorityEmoji(task.priority)} {task.priority}
                        </Text>
                        <Text style={styles.metaText}>
                          📅 {task.dueDate}
                        </Text>
                      </View>
                    </ExpressiveCard>
                  ))}
                </View>
              </View>
            )}

            {/* To Do Tasks */}
            {todoTasks.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>To Do</Text>
                <View style={styles.taskList}>
                  {todoTasks.map((task) => (
                    <ExpressiveCard
                      key={task.id}
                      title={task.title}
                      description={task.description}
                      variant="outlined"
                    >
                      <View style={styles.taskMeta}>
                        <Text style={styles.metaText}>
                          {getStatusEmoji(task.status)} {task.status}
                        </Text>
                        <Text style={styles.metaText}>
                          {getPriorityEmoji(task.priority)} {task.priority}
                        </Text>
                        <Text style={styles.metaText}>
                          📅 {task.dueDate}
                        </Text>
                      </View>
                    </ExpressiveCard>
                  ))}
                </View>
              </View>
            )}

            {/* Completed Tasks */}
            {completedTasks.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Completed</Text>
                <View style={styles.taskList}>
                  {completedTasks.map((task) => (
                    <ExpressiveCard
                      key={task.id}
                      title={task.title}
                      description={task.description}
                      variant="filled"
                      style={{ opacity: 0.7 }}
                    >
                      <View style={styles.taskMeta}>
                        <Text style={styles.metaText}>
                          {getStatusEmoji(task.status)} {task.status}
                        </Text>
                        <Text style={styles.metaText}>
                          {getPriorityEmoji(task.priority)} {task.priority}
                        </Text>
                        <Text style={styles.metaText}>
                          📅 {task.dueDate}
                        </Text>
                      </View>
                    </ExpressiveCard>
                  ))}
                </View>
              </View>
            )}
          </>
        ) : (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kanban Board</Text>
            <ExpressiveCard
              title="Kanban View"
              subtitle="Coming Soon"
              description="The Kanban board view will be implemented in the next phase."
              variant="outlined"
            />
          </View>
        )}
      </ScrollView>

      {/* Floating Action Button */}
      <ExpressiveFAB
        icon={<Text style={{ fontSize: 24, color: theme.colors.onPrimaryContainer }}>➕</Text>}
        label="Add Task"
        extended={true}
        position="bottomRight"
        onPress={() => console.log('Add new task')}
      />
    </View>
  );
};

export default TasksScreen;
