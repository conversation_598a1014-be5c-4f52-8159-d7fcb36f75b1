import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
  Text,
  Surface,
  SegmentedButtons,
  FAB,
  Portal,
  Modal,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { useTimerStore } from '../stores/timerStore';
import { useSubjectStore } from '../stores/subjectStore';
import { useBackgroundTimer, useTimerNotifications } from '../hooks';
import {
  ExpressiveTimerCard,
  ExpressiveTimerControls,
  PomodoroTimer,
  StopwatchTimer,
  SessionFeedbackModal,
  SubjectSelector,
} from '../components/timer';
import { DEFAULT_SUBJECT_COLORS } from '../constants/colorPalette';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const TimerScreen: React.FC = () => {
  const theme = useDynamicTheme();
  const insets = useSafeAreaInsets();
  const { mode, status, selectedSubject, setSubject } = useTimerStore();
  const {
    subjects,
    createSubject,
    selectSubject,
    updateSubjectStats,
    loadSubjects,
    getFilteredSubjects,
  } = useSubjectStore();

  // Hooks
  useBackgroundTimer();
  const { sendSessionCompleteNotification } = useTimerNotifications();

  // Local state
  const [showSubjectSelector, setShowSubjectSelector] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [lastSessionData, setLastSessionData] = useState(null);

  // Load subjects on mount
  useEffect(() => {
    loadSubjects();
  }, [loadSubjects]);

  // Handle mode change
  const handleModeChange = (newMode: string) => {
    const timerStore = useTimerStore.getState();
    timerStore.setMode(newMode as 'pomodoro' | 'stopwatch');
  };

  // Handle subject selection
  const handleSubjectSelect = (subject: any) => {
    selectSubject(subject);
    setSubject(subject.name);
    setShowSubjectSelector(false);
  };

  // Handle add subject
  const handleAddSubject = async (name: string, color: string) => {
    const newSubject = await createSubject(name, color || getRandomColor());
    if (newSubject) {
      // Auto-select the newly created subject
      handleSubjectSelect(newSubject);
    }
  };

  // Get random color for new subjects
  const getRandomColor = () => {
    const usedColors = subjects.map(s => s.color);
    const availableColors = DEFAULT_SUBJECT_COLORS.filter(color => !usedColors.includes(color));
    return availableColors.length > 0
      ? availableColors[Math.floor(Math.random() * availableColors.length)]
      : DEFAULT_SUBJECT_COLORS[Math.floor(Math.random() * DEFAULT_SUBJECT_COLORS.length)];
  };

  // Handle session completion
  const handleSessionComplete = (sessionData: any) => {
    setLastSessionData(sessionData);
    setShowFeedbackModal(true);
    sendSessionCompleteNotification(sessionData);

    // Update subject statistics
    const currentSubject = subjects.find(s => s.name === selectedSubject);
    if (currentSubject) {
      updateSubjectStats(currentSubject.id, sessionData);
    }
  };

  // Handle feedback submission
  const handleFeedbackSubmit = (feedback: any) => {
    console.log('Session feedback:', feedback);
    // Here you would typically save the feedback to the database
    setShowFeedbackModal(false);
    setLastSessionData(null);
  };

  // Handle feedback skip
  const handleFeedbackSkip = () => {
    setShowFeedbackModal(false);
    setLastSessionData(null);
  };

  // Get current subject object
  const getCurrentSubject = () => {
    return subjects.find(s => s.name === selectedSubject) || null;
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    statusBar: {
      height: insets.top,
      backgroundColor: theme.colors.surface,
    },
    header: {
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 20,
      paddingVertical: 16,
      elevation: 2,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
    modeSelector: {
      marginTop: 16,
    },
    content: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingBottom: 100, // Space for FAB
    },
    timerSection: {
      flex: 1,
      justifyContent: 'center',
      minHeight: screenHeight * 0.6,
    },
    subjectInfo: {
      margin: 20,
      padding: 16,
      borderRadius: 16,
      backgroundColor: theme.colors.primaryContainer,
      alignItems: 'center',
    },
    subjectInfoText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onPrimaryContainer,
    },
    subjectMetaText: {
      fontSize: 14,
      fontWeight: '400',
      marginTop: 4,
      opacity: 0.8,
    },
    noSubjectInfo: {
      margin: 20,
      padding: 16,
      borderRadius: 16,
      backgroundColor: theme.colors.errorContainer,
      alignItems: 'center',
    },
    noSubjectText: {
      fontSize: 14,
      color: theme.colors.onErrorContainer,
      textAlign: 'center',
    },
    fab: {
      position: 'absolute',
      bottom: 16 + insets.bottom,
      right: 16,
      backgroundColor: theme.colors.primaryContainer,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    subjectModal: {
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      maxHeight: screenHeight * 0.8,
      paddingBottom: insets.bottom,
    },
  });

  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <View style={styles.statusBar} />
      <StatusBar
        backgroundColor={theme.colors.surface}
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
      />

      {/* Header */}
      <Surface style={styles.header} elevation={2}>
        <Text style={styles.headerTitle}>Focus Timer</Text>

        {/* Mode Selector */}
        <SegmentedButtons
          value={mode}
          onValueChange={handleModeChange}
          buttons={[
            {
              value: 'pomodoro',
              label: 'Pomodoro',
              icon: 'timer',
            },
            {
              value: 'stopwatch',
              label: 'Stopwatch',
              icon: 'timer-outline',
            },
          ]}
          style={styles.modeSelector}
          disabled={status === 'running'}
        />
      </Surface>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Subject Info */}
        {selectedSubject ? (
          <Surface style={[styles.subjectInfo, { backgroundColor: getCurrentSubject()?.color + '20' || theme.colors.primaryContainer }]}>
            <Text style={[styles.subjectInfoText, { color: getCurrentSubject()?.color || theme.colors.onPrimaryContainer }]}>
              📚 Studying: {selectedSubject}
            </Text>
            {getCurrentSubject()?.todayTime && (
              <Text style={[styles.subjectMetaText, { color: getCurrentSubject()?.color || theme.colors.onPrimaryContainer }]}>
                Today: {Math.floor((getCurrentSubject()?.todayTime || 0) / 60)} minutes
              </Text>
            )}
          </Surface>
        ) : (
          <Surface style={styles.noSubjectInfo}>
            <Text style={styles.noSubjectText}>
              📖 Select a subject to start your study session
            </Text>
          </Surface>
        )}

        {/* Timer Section */}
        <View style={styles.timerSection}>
          {mode === 'pomodoro' ? (
            <PomodoroTimer
              onSessionComplete={handleSessionComplete}
              onPhaseChange={(phase) => console.log('Phase changed:', phase)}
            />
          ) : (
            <StopwatchTimer
              onSessionComplete={handleSessionComplete}
              onNotificationInterval={(interval) => console.log('Interval:', interval)}
            />
          )}
        </View>
      </ScrollView>

      {/* Subject Selection FAB */}
      <FAB
        icon="book"
        label={selectedSubject ? 'Change Subject' : 'Select Subject'}
        onPress={() => setShowSubjectSelector(true)}
        style={styles.fab}
        variant="primary"
      />

      {/* Subject Selector Modal */}
      <Portal>
        <Modal
          visible={showSubjectSelector}
          onDismiss={() => setShowSubjectSelector(false)}
          contentContainerStyle={styles.modalOverlay}
        >
          <View style={styles.subjectModal}>
            <SubjectSelector
              subjects={getFilteredSubjects()}
              onSubjectSelect={handleSubjectSelect}
              onAddSubject={handleAddSubject}
              showStats={true}
            />
          </View>
        </Modal>
      </Portal>

      {/* Session Feedback Modal */}
      <SessionFeedbackModal
        visible={showFeedbackModal}
        sessionData={lastSessionData}
        onSubmit={handleFeedbackSubmit}
        onSkip={handleFeedbackSkip}
        onClose={() => setShowFeedbackModal(false)}
      />
    </View>
  );
};

export default TimerScreen;
