import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import {
  Text,
  Surface,
  SegmentedButtons,
  FAB,
  Portal,
  Modal,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { useTimerStore } from '../stores/timerStore';
import { useBackgroundTimer, useTimerNotifications } from '../hooks';
import {
  ExpressiveTimerCard,
  ExpressiveTimerControls,
  PomodoroTimer,
  StopwatchTimer,
  SessionFeedbackModal,
  SubjectSelector,
} from '../components/timer';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Mock subjects data - in real app this would come from a store/API
const mockSubjects = [
  { id: '1', name: 'Mathematics', color: '#6750A4', totalTime: 7200, sessionsToday: 3 },
  { id: '2', name: 'Physics', color: '#7C4DFF', totalTime: 5400, sessionsToday: 2 },
  { id: '3', name: 'Chemistry', color: '#3F51B5', totalTime: 3600, sessionsToday: 1 },
  { id: '4', name: 'Biology', color: '#009688', totalTime: 1800, sessionsToday: 1 },
];

export const TimerScreen: React.FC = () => {
  const theme = useDynamicTheme();
  const insets = useSafeAreaInsets();
  const { mode, status, selectedSubject } = useTimerStore();

  // Hooks
  useBackgroundTimer();
  const { sendSessionCompleteNotification } = useTimerNotifications();

  // Local state
  const [subjects, setSubjects] = useState(mockSubjects);
  const [showSubjectSelector, setShowSubjectSelector] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [lastSessionData, setLastSessionData] = useState(null);

  // Handle mode change
  const handleModeChange = (newMode: string) => {
    const timerStore = useTimerStore.getState();
    timerStore.setMode(newMode as 'pomodoro' | 'stopwatch');
  };

  // Handle subject selection
  const handleSubjectSelect = (subject: any) => {
    setShowSubjectSelector(false);
  };

  // Handle add subject
  const handleAddSubject = (name: string, color: string) => {
    const newSubject = {
      id: Date.now().toString(),
      name,
      color,
      totalTime: 0,
      sessionsToday: 0,
    };
    setSubjects(prev => [...prev, newSubject]);
  };

  // Handle session completion
  const handleSessionComplete = (sessionData: any) => {
    setLastSessionData(sessionData);
    setShowFeedbackModal(true);
    sendSessionCompleteNotification(sessionData);
  };

  // Handle feedback submission
  const handleFeedbackSubmit = (feedback: any) => {
    console.log('Session feedback:', feedback);
    setShowFeedbackModal(false);
    setLastSessionData(null);
  };

  // Handle feedback skip
  const handleFeedbackSkip = () => {
    setShowFeedbackModal(false);
    setLastSessionData(null);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    statusBar: {
      height: insets.top,
      backgroundColor: theme.colors.surface,
    },
    header: {
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 20,
      paddingVertical: 16,
      elevation: 2,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
    modeSelector: {
      marginTop: 16,
    },
    content: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingBottom: 100, // Space for FAB
    },
    timerSection: {
      flex: 1,
      justifyContent: 'center',
      minHeight: screenHeight * 0.6,
    },
    subjectInfo: {
      margin: 20,
      padding: 16,
      borderRadius: 16,
      backgroundColor: theme.colors.primaryContainer,
      alignItems: 'center',
    },
    subjectInfoText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onPrimaryContainer,
    },
    noSubjectInfo: {
      margin: 20,
      padding: 16,
      borderRadius: 16,
      backgroundColor: theme.colors.errorContainer,
      alignItems: 'center',
    },
    noSubjectText: {
      fontSize: 14,
      color: theme.colors.onErrorContainer,
      textAlign: 'center',
    },
    fab: {
      position: 'absolute',
      bottom: 16 + insets.bottom,
      right: 16,
      backgroundColor: theme.colors.primaryContainer,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    subjectModal: {
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      maxHeight: screenHeight * 0.8,
      paddingBottom: insets.bottom,
    },
  });

  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <View style={styles.statusBar} />
      <StatusBar
        backgroundColor={theme.colors.surface}
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
      />

      {/* Header */}
      <Surface style={styles.header} elevation={2}>
        <Text style={styles.headerTitle}>Focus Timer</Text>

        {/* Mode Selector */}
        <SegmentedButtons
          value={mode}
          onValueChange={handleModeChange}
          buttons={[
            {
              value: 'pomodoro',
              label: 'Pomodoro',
              icon: 'timer',
            },
            {
              value: 'stopwatch',
              label: 'Stopwatch',
              icon: 'timer-outline',
            },
          ]}
          style={styles.modeSelector}
          disabled={status === 'running'}
        />
      </Surface>

      {/* Content */}
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Subject Info */}
        {selectedSubject ? (
          <Surface style={styles.subjectInfo}>
            <Text style={styles.subjectInfoText}>
              Studying: {selectedSubject}
            </Text>
          </Surface>
        ) : (
          <Surface style={styles.noSubjectInfo}>
            <Text style={styles.noSubjectText}>
              Select a subject to start your study session
            </Text>
          </Surface>
        )}

        {/* Timer Section */}
        <View style={styles.timerSection}>
          {mode === 'pomodoro' ? (
            <PomodoroTimer
              onSessionComplete={handleSessionComplete}
              onPhaseChange={(phase) => console.log('Phase changed:', phase)}
            />
          ) : (
            <StopwatchTimer
              onSessionComplete={handleSessionComplete}
              onNotificationInterval={(interval) => console.log('Interval:', interval)}
            />
          )}
        </View>
      </ScrollView>

      {/* Subject Selection FAB */}
      <FAB
        icon="book"
        label={selectedSubject ? 'Change Subject' : 'Select Subject'}
        onPress={() => setShowSubjectSelector(true)}
        style={styles.fab}
        variant="primary"
      />

      {/* Subject Selector Modal */}
      <Portal>
        <Modal
          visible={showSubjectSelector}
          onDismiss={() => setShowSubjectSelector(false)}
          contentContainerStyle={styles.modalOverlay}
        >
          <View style={styles.subjectModal}>
            <SubjectSelector
              subjects={subjects}
              onSubjectSelect={handleSubjectSelect}
              onAddSubject={handleAddSubject}
              showStats={true}
            />
          </View>
        </Modal>
      </Portal>

      {/* Session Feedback Modal */}
      <SessionFeedbackModal
        visible={showFeedbackModal}
        sessionData={lastSessionData}
        onSubmit={handleFeedbackSubmit}
        onSkip={handleFeedbackSkip}
        onClose={() => setShowFeedbackModal(false)}
      />
    </View>
  );
};

export default TimerScreen;
