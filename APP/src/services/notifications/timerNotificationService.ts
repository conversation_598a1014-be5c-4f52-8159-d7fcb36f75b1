import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { TimerPhase, TimerMode } from '../../stores/timerStore';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface TimerNotificationConfig {
  title: string;
  body: string;
  sound?: boolean;
  vibrate?: boolean;
  categoryIdentifier?: string;
  data?: any;
}

export interface ScheduledNotification {
  id: string;
  trigger: Date;
  config: TimerNotificationConfig;
}

class TimerNotificationService {
  private isInitialized = false;
  private scheduledNotifications: Map<string, string> = new Map(); // notificationId -> expo notification id

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) return true;

    try {
      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Notification permissions not granted');
        return false;
      }

      // Configure notification categories for interactive notifications
      await this.setupNotificationCategories();

      this.isInitialized = true;
      console.log('Timer notification service initialized');
      return true;
    } catch (error) {
      console.error('Failed to initialize timer notification service:', error);
      return false;
    }
  }

  /**
   * Setup notification categories for interactive notifications
   */
  private async setupNotificationCategories(): Promise<void> {
    if (Platform.OS === 'ios') {
      await Notifications.setNotificationCategoryAsync('TIMER_PHASE_COMPLETE', [
        {
          identifier: 'START_NEXT_PHASE',
          buttonTitle: 'Start Next Phase',
          options: { opensAppToForeground: true },
        },
        {
          identifier: 'SKIP_PHASE',
          buttonTitle: 'Skip',
          options: { opensAppToForeground: false },
        },
      ]);

      await Notifications.setNotificationCategoryAsync('TIMER_INTERVAL', [
        {
          identifier: 'CONTINUE_STUDYING',
          buttonTitle: 'Continue',
          options: { opensAppToForeground: false },
        },
        {
          identifier: 'TAKE_BREAK',
          buttonTitle: 'Take Break',
          options: { opensAppToForeground: true },
        },
      ]);
    }
  }

  /**
   * Schedule a phase completion notification
   */
  async schedulePhaseCompleteNotification(
    phase: TimerPhase,
    nextPhase: TimerPhase,
    delaySeconds: number,
    subject?: string
  ): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const notificationId = `phase_complete_${Date.now()}`;
    const config = this.getPhaseCompleteNotificationConfig(phase, nextPhase, subject);

    try {
      const trigger = new Date(Date.now() + delaySeconds * 1000);
      const expoNotificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: config.title,
          body: config.body,
          sound: config.sound ? 'default' : false,
          categoryIdentifier: config.categoryIdentifier,
          data: config.data,
        },
        trigger,
      });

      this.scheduledNotifications.set(notificationId, expoNotificationId);
      console.log(`Scheduled phase complete notification: ${notificationId}`);
      return notificationId;
    } catch (error) {
      console.error('Failed to schedule phase complete notification:', error);
      return null;
    }
  }

  /**
   * Schedule an interval notification for stopwatch mode
   */
  async scheduleIntervalNotification(
    intervalMinutes: number,
    delaySeconds: number,
    subject?: string
  ): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const notificationId = `interval_${intervalMinutes}_${Date.now()}`;
    const config = this.getIntervalNotificationConfig(intervalMinutes, subject);

    try {
      const trigger = new Date(Date.now() + delaySeconds * 1000);
      const expoNotificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: config.title,
          body: config.body,
          sound: config.sound ? 'default' : false,
          categoryIdentifier: config.categoryIdentifier,
          data: config.data,
        },
        trigger,
      });

      this.scheduledNotifications.set(notificationId, expoNotificationId);
      console.log(`Scheduled interval notification: ${notificationId}`);
      return notificationId;
    } catch (error) {
      console.error('Failed to schedule interval notification:', error);
      return null;
    }
  }

  /**
   * Schedule a session reminder notification
   */
  async scheduleSessionReminder(
    title: string,
    body: string,
    delaySeconds: number,
    data?: any
  ): Promise<string | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const notificationId = `reminder_${Date.now()}`;

    try {
      const trigger = new Date(Date.now() + delaySeconds * 1000);
      const expoNotificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          sound: 'default',
          data,
        },
        trigger,
      });

      this.scheduledNotifications.set(notificationId, expoNotificationId);
      console.log(`Scheduled session reminder: ${notificationId}`);
      return notificationId;
    } catch (error) {
      console.error('Failed to schedule session reminder:', error);
      return null;
    }
  }

  /**
   * Send immediate notification
   */
  async sendImmediateNotification(config: TimerNotificationConfig): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: config.title,
          body: config.body,
          sound: config.sound ? 'default' : false,
          categoryIdentifier: config.categoryIdentifier,
          data: config.data,
        },
        trigger: null, // Send immediately
      });

      console.log('Sent immediate notification');
    } catch (error) {
      console.error('Failed to send immediate notification:', error);
    }
  }

  /**
   * Cancel a specific notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    const expoNotificationId = this.scheduledNotifications.get(notificationId);
    if (expoNotificationId) {
      try {
        await Notifications.cancelScheduledNotificationAsync(expoNotificationId);
        this.scheduledNotifications.delete(notificationId);
        console.log(`Cancelled notification: ${notificationId}`);
      } catch (error) {
        console.error(`Failed to cancel notification ${notificationId}:`, error);
      }
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      this.scheduledNotifications.clear();
      console.log('Cancelled all timer notifications');
    } catch (error) {
      console.error('Failed to cancel all notifications:', error);
    }
  }

  /**
   * Get all scheduled notifications
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Failed to get scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Get phase completion notification configuration
   */
  private getPhaseCompleteNotificationConfig(
    phase: TimerPhase,
    nextPhase: TimerPhase,
    subject?: string
  ): TimerNotificationConfig {
    const subjectText = subject ? ` for ${subject}` : '';
    
    switch (phase) {
      case 'work':
        return {
          title: '🎉 Work Session Complete!',
          body: `Great job! Time for a ${nextPhase === 'longBreak' ? 'long' : 'short'} break.`,
          sound: true,
          categoryIdentifier: 'TIMER_PHASE_COMPLETE',
          data: { phase, nextPhase, subject },
        };
      case 'shortBreak':
        return {
          title: '⏰ Break Time Over',
          body: `Ready to get back to work${subjectText}?`,
          sound: true,
          categoryIdentifier: 'TIMER_PHASE_COMPLETE',
          data: { phase, nextPhase, subject },
        };
      case 'longBreak':
        return {
          title: '🚀 Long Break Complete',
          body: `Refreshed and ready for the next session${subjectText}!`,
          sound: true,
          categoryIdentifier: 'TIMER_PHASE_COMPLETE',
          data: { phase, nextPhase, subject },
        };
      default:
        return {
          title: 'Timer Complete',
          body: 'Your timer session has finished.',
          sound: true,
          data: { phase, nextPhase, subject },
        };
    }
  }

  /**
   * Get interval notification configuration
   */
  private getIntervalNotificationConfig(
    intervalMinutes: number,
    subject?: string
  ): TimerNotificationConfig {
    const subjectText = subject ? ` on ${subject}` : '';
    
    return {
      title: `📚 ${intervalMinutes} Minutes of Focus!`,
      body: `You've been studying${subjectText} for ${intervalMinutes} minutes. Keep it up!`,
      sound: true,
      categoryIdentifier: 'TIMER_INTERVAL',
      data: { intervalMinutes, subject, type: 'interval' },
    };
  }

  /**
   * Handle notification response (when user taps notification or action button)
   */
  handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const { notification, actionIdentifier } = response;
    const data = notification.request.content.data;

    console.log('Notification response:', { actionIdentifier, data });

    // Handle different action types
    switch (actionIdentifier) {
      case 'START_NEXT_PHASE':
        // Start the next phase
        this.handleStartNextPhase(data);
        break;
      case 'SKIP_PHASE':
        // Skip the current phase
        this.handleSkipPhase(data);
        break;
      case 'CONTINUE_STUDYING':
        // Continue studying (dismiss notification)
        break;
      case 'TAKE_BREAK':
        // Suggest taking a break
        this.handleTakeBreak(data);
        break;
      default:
        // Default tap action - open app
        break;
    }
  }

  /**
   * Handle start next phase action
   */
  private handleStartNextPhase(data: any): void {
    // This would typically trigger a store action or navigation
    console.log('Starting next phase:', data);
  }

  /**
   * Handle skip phase action
   */
  private handleSkipPhase(data: any): void {
    // This would typically trigger a store action
    console.log('Skipping phase:', data);
  }

  /**
   * Handle take break action
   */
  private handleTakeBreak(data: any): void {
    // This would typically pause the timer or suggest a break
    console.log('Taking break:', data);
  }
}

// Export singleton instance
export const timerNotificationService = new TimerNotificationService();
