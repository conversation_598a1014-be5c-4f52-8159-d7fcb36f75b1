import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';
import { useTimerStore } from '../../stores/timerStore';

const BACKGROUND_TIMER_KEY = 'background_timer_state';
const BACKGROUND_START_TIME_KEY = 'background_start_time';

export interface BackgroundTimerState {
  isRunning: boolean;
  mode: 'pomodoro' | 'stopwatch';
  currentPhase: 'work' | 'shortBreak' | 'longBreak' | 'pause';
  elapsedTime: number;
  startTime: number;
  backgroundStartTime: number;
  selectedSubject: string | null;
  currentTaskName: string | null;
  pomodoroCount: number;
  workDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
}

class BackgroundTimerService {
  private appStateSubscription: any = null;
  private backgroundStartTime: number = 0;
  private isInitialized: boolean = false;

  /**
   * Initialize the background timer service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load persisted state on app start
      await this.loadPersistedState();
      
      // Set up app state listener
      this.setupAppStateListener();
      
      this.isInitialized = true;
      console.log('Background timer service initialized');
    } catch (error) {
      console.error('Failed to initialize background timer service:', error);
    }
  }

  /**
   * Clean up the service
   */
  cleanup(): void {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
    this.isInitialized = false;
  }

  /**
   * Set up app state change listener
   */
  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );
  }

  /**
   * Handle app state changes
   */
  private async handleAppStateChange(nextAppState: AppStateStatus): Promise<void> {
    const timerStore = useTimerStore.getState();

    try {
      if (nextAppState === 'background') {
        // App going to background
        if (timerStore.status === 'running') {
          await this.saveBackgroundState();
        }
      } else if (nextAppState === 'active') {
        // App coming to foreground
        await this.handleForegroundReturn();
      }
    } catch (error) {
      console.error('Error handling app state change:', error);
    }
  }

  /**
   * Save timer state when app goes to background
   */
  private async saveBackgroundState(): Promise<void> {
    const timerStore = useTimerStore.getState();
    this.backgroundStartTime = Date.now();

    const backgroundState: BackgroundTimerState = {
      isRunning: timerStore.status === 'running',
      mode: timerStore.mode,
      currentPhase: timerStore.currentPhase,
      elapsedTime: timerStore.elapsedTime,
      startTime: timerStore.sessionStartTime?.getTime() || Date.now(),
      backgroundStartTime: this.backgroundStartTime,
      selectedSubject: timerStore.selectedSubject,
      currentTaskName: timerStore.currentTaskName,
      pomodoroCount: timerStore.pomodoroCount,
      workDuration: timerStore.workDuration,
      shortBreakDuration: timerStore.shortBreakDuration,
      longBreakDuration: timerStore.longBreakDuration,
    };

    await AsyncStorage.setItem(BACKGROUND_TIMER_KEY, JSON.stringify(backgroundState));
    await AsyncStorage.setItem(BACKGROUND_START_TIME_KEY, this.backgroundStartTime.toString());
    
    console.log('Background timer state saved');
  }

  /**
   * Handle app returning to foreground
   */
  private async handleForegroundReturn(): Promise<void> {
    try {
      const backgroundStateStr = await AsyncStorage.getItem(BACKGROUND_TIMER_KEY);
      const backgroundStartTimeStr = await AsyncStorage.getItem(BACKGROUND_START_TIME_KEY);

      if (!backgroundStateStr || !backgroundStartTimeStr) {
        return;
      }

      const backgroundState: BackgroundTimerState = JSON.parse(backgroundStateStr);
      const backgroundStartTime = parseInt(backgroundStartTimeStr, 10);

      if (!backgroundState.isRunning) {
        // Clear background state if timer wasn't running
        await this.clearBackgroundState();
        return;
      }

      // Calculate time spent in background
      const currentTime = Date.now();
      const backgroundDuration = Math.floor((currentTime - backgroundStartTime) / 1000);

      if (backgroundDuration > 0) {
        await this.updateTimerWithBackgroundTime(backgroundState, backgroundDuration);
      }

      // Clear background state
      await this.clearBackgroundState();
    } catch (error) {
      console.error('Error handling foreground return:', error);
    }
  }

  /**
   * Update timer with time spent in background
   */
  private async updateTimerWithBackgroundTime(
    backgroundState: BackgroundTimerState,
    backgroundDuration: number
  ): Promise<void> {
    const timerStore = useTimerStore.getState();
    const newElapsedTime = backgroundState.elapsedTime + backgroundDuration;

    if (backgroundState.mode === 'pomodoro') {
      // Handle Pomodoro timer background time
      const totalPhaseDuration = this.getPhaseDuration(
        backgroundState.currentPhase,
        backgroundState
      );

      if (newElapsedTime >= totalPhaseDuration) {
        // Phase completed in background
        await this.handlePhaseCompletionInBackground(backgroundState, newElapsedTime);
      } else {
        // Update timer with background time
        timerStore.updateElapsedTime(newElapsedTime);
        timerStore.updateDisplayTime(totalPhaseDuration - newElapsedTime);
      }
    } else {
      // Handle Stopwatch timer background time
      timerStore.updateElapsedTime(newElapsedTime);
      timerStore.updateDisplayTime(newElapsedTime);
    }

    console.log(`Updated timer with ${backgroundDuration} seconds of background time`);
  }

  /**
   * Handle phase completion that occurred in background
   */
  private async handlePhaseCompletionInBackground(
    backgroundState: BackgroundTimerState,
    totalElapsedTime: number
  ): Promise<void> {
    const timerStore = useTimerStore.getState();
    
    // Calculate how many phases were completed
    let remainingTime = totalElapsedTime;
    let currentPhase = backgroundState.currentPhase;
    let pomodoroCount = backgroundState.pomodoroCount;

    while (remainingTime > 0) {
      const phaseDuration = this.getPhaseDuration(currentPhase, backgroundState);
      
      if (remainingTime >= phaseDuration) {
        // Phase completed
        remainingTime -= phaseDuration;
        
        if (currentPhase === 'work') {
          pomodoroCount++;
          // Complete the work session
          timerStore.completeSession();
        }

        // Move to next phase
        currentPhase = this.getNextPhase(currentPhase, pomodoroCount);
      } else {
        // Partial phase completion
        break;
      }
    }

    // Update timer state
    const finalPhaseDuration = this.getPhaseDuration(currentPhase, backgroundState);
    timerStore.updateElapsedTime(remainingTime);
    timerStore.updateDisplayTime(Math.max(finalPhaseDuration - remainingTime, 0));
    
    // Update phase and pomodoro count if changed
    if (currentPhase !== backgroundState.currentPhase) {
      timerStore.skipPhase(); // This will handle phase transitions
    }
  }

  /**
   * Get phase duration in seconds
   */
  private getPhaseDuration(
    phase: 'work' | 'shortBreak' | 'longBreak' | 'pause',
    state: BackgroundTimerState
  ): number {
    switch (phase) {
      case 'work':
        return state.workDuration * 60;
      case 'shortBreak':
        return state.shortBreakDuration * 60;
      case 'longBreak':
        return state.longBreakDuration * 60;
      default:
        return 0;
    }
  }

  /**
   * Get next phase in Pomodoro cycle
   */
  private getNextPhase(
    currentPhase: 'work' | 'shortBreak' | 'longBreak' | 'pause',
    pomodoroCount: number
  ): 'work' | 'shortBreak' | 'longBreak' | 'pause' {
    if (currentPhase === 'work') {
      return pomodoroCount % 4 === 0 ? 'longBreak' : 'shortBreak';
    }
    return 'work';
  }

  /**
   * Load persisted state on app start
   */
  private async loadPersistedState(): Promise<void> {
    try {
      const timerStore = useTimerStore.getState();
      await timerStore.loadPersistedState();
      
      // Check if there's any background state to restore
      const backgroundStateStr = await AsyncStorage.getItem(BACKGROUND_TIMER_KEY);
      if (backgroundStateStr) {
        // There was a background session, handle it
        await this.handleForegroundReturn();
      }
    } catch (error) {
      console.error('Error loading persisted state:', error);
    }
  }

  /**
   * Clear background state
   */
  private async clearBackgroundState(): Promise<void> {
    try {
      await AsyncStorage.removeItem(BACKGROUND_TIMER_KEY);
      await AsyncStorage.removeItem(BACKGROUND_START_TIME_KEY);
    } catch (error) {
      console.error('Error clearing background state:', error);
    }
  }

  /**
   * Force save current timer state
   */
  async saveCurrentState(): Promise<void> {
    const timerStore = useTimerStore.getState();
    await timerStore.persistState();
  }

  /**
   * Check if timer is running in background
   */
  async isRunningInBackground(): Promise<boolean> {
    try {
      const backgroundStateStr = await AsyncStorage.getItem(BACKGROUND_TIMER_KEY);
      if (backgroundStateStr) {
        const backgroundState: BackgroundTimerState = JSON.parse(backgroundStateStr);
        return backgroundState.isRunning;
      }
      return false;
    } catch (error) {
      console.error('Error checking background state:', error);
      return false;
    }
  }

  /**
   * Get background session duration
   */
  async getBackgroundDuration(): Promise<number> {
    try {
      const backgroundStartTimeStr = await AsyncStorage.getItem(BACKGROUND_START_TIME_KEY);
      if (backgroundStartTimeStr) {
        const backgroundStartTime = parseInt(backgroundStartTimeStr, 10);
        return Math.floor((Date.now() - backgroundStartTime) / 1000);
      }
      return 0;
    } catch (error) {
      console.error('Error getting background duration:', error);
      return 0;
    }
  }
}

// Export singleton instance
export const backgroundTimerService = new BackgroundTimerService();
