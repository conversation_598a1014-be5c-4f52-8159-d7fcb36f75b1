import { TimerPhase, TimerMode } from '../stores/timerStore';

/**
 * Formats time in seconds to MM:SS or HH:MM:SS format
 */
export const formatTime = (seconds: number, includeHours = false): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (includeHours || hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  return `${minutes.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
};

/**
 * Formats time in seconds to a human-readable string
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  const parts: string[] = [];
  
  if (hours > 0) {
    parts.push(`${hours}h`);
  }
  if (minutes > 0) {
    parts.push(`${minutes}m`);
  }
  if (secs > 0 || parts.length === 0) {
    parts.push(`${secs}s`);
  }

  return parts.join(' ');
};

/**
 * Converts minutes to seconds
 */
export const minutesToSeconds = (minutes: number): number => minutes * 60;

/**
 * Converts seconds to minutes (rounded)
 */
export const secondsToMinutes = (seconds: number): number => Math.round(seconds / 60);

/**
 * Gets the duration for a specific timer phase
 */
export const getPhaseDuration = (
  phase: TimerPhase,
  settings: {
    workDuration: number;
    shortBreakDuration: number;
    longBreakDuration: number;
  }
): number => {
  switch (phase) {
    case 'work':
      return minutesToSeconds(settings.workDuration);
    case 'shortBreak':
      return minutesToSeconds(settings.shortBreakDuration);
    case 'longBreak':
      return minutesToSeconds(settings.longBreakDuration);
    case 'pause':
      return 0;
    default:
      return minutesToSeconds(settings.workDuration);
  }
};

/**
 * Determines the next phase in a Pomodoro cycle
 */
export const getNextPomodoroPhase = (
  currentPhase: TimerPhase,
  pomodoroCount: number,
  longBreakInterval: number = 4
): TimerPhase => {
  if (currentPhase === 'work') {
    const nextCount = pomodoroCount + 1;
    return nextCount % longBreakInterval === 0 ? 'longBreak' : 'shortBreak';
  }
  return 'work';
};

/**
 * Calculates progress percentage for current phase
 */
export const calculatePhaseProgress = (
  elapsedTime: number,
  totalDuration: number
): number => {
  if (totalDuration === 0) return 0;
  return Math.min((elapsedTime / totalDuration) * 100, 100);
};

/**
 * Calculates remaining time for current phase
 */
export const calculateRemainingTime = (
  elapsedTime: number,
  totalDuration: number
): number => {
  return Math.max(totalDuration - elapsedTime, 0);
};

/**
 * Checks if it's time for a notification based on intervals
 */
export const shouldNotify = (
  elapsedTime: number,
  lastNotificationTime: number,
  intervals: number[]
): boolean => {
  const elapsedMinutes = Math.floor(elapsedTime / 60);
  const lastNotificationMinutes = Math.floor(lastNotificationTime / 60);
  
  return intervals.some(interval => 
    elapsedMinutes >= interval && lastNotificationMinutes < interval
  );
};

/**
 * Gets the next notification time
 */
export const getNextNotificationTime = (
  elapsedTime: number,
  intervals: number[]
): number | null => {
  const elapsedMinutes = Math.floor(elapsedTime / 60);
  const nextInterval = intervals.find(interval => interval > elapsedMinutes);
  return nextInterval ? minutesToSeconds(nextInterval) : null;
};

/**
 * Validates timer settings
 */
export const validateTimerSettings = (settings: {
  workDuration?: number;
  shortBreakDuration?: number;
  longBreakDuration?: number;
  longBreakInterval?: number;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (settings.workDuration !== undefined) {
    if (settings.workDuration < 1 || settings.workDuration > 120) {
      errors.push('Work duration must be between 1 and 120 minutes');
    }
  }

  if (settings.shortBreakDuration !== undefined) {
    if (settings.shortBreakDuration < 1 || settings.shortBreakDuration > 30) {
      errors.push('Short break duration must be between 1 and 30 minutes');
    }
  }

  if (settings.longBreakDuration !== undefined) {
    if (settings.longBreakDuration < 1 || settings.longBreakDuration > 60) {
      errors.push('Long break duration must be between 1 and 60 minutes');
    }
  }

  if (settings.longBreakInterval !== undefined) {
    if (settings.longBreakInterval < 2 || settings.longBreakInterval > 10) {
      errors.push('Long break interval must be between 2 and 10 pomodoros');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Calculates daily study statistics
 */
export const calculateDailyStats = (sessions: Array<{
  duration: number;
  mode: TimerMode;
  phase: TimerPhase;
  completed: boolean;
  startTime: Date;
}>): {
  totalStudyTime: number;
  pomodoroSessions: number;
  stopwatchSessions: number;
  completedSessions: number;
  averageSessionLength: number;
} => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const todaySessions = sessions.filter(session => {
    const sessionDate = new Date(session.startTime);
    sessionDate.setHours(0, 0, 0, 0);
    return sessionDate.getTime() === today.getTime();
  });

  const studySessions = todaySessions.filter(
    session => session.phase === 'work' || session.mode === 'stopwatch'
  );

  const totalStudyTime = studySessions.reduce(
    (total, session) => total + session.duration,
    0
  );

  const pomodoroSessions = studySessions.filter(
    session => session.mode === 'pomodoro'
  ).length;

  const stopwatchSessions = studySessions.filter(
    session => session.mode === 'stopwatch'
  ).length;

  const completedSessions = studySessions.filter(
    session => session.completed
  ).length;

  const averageSessionLength = studySessions.length > 0
    ? totalStudyTime / studySessions.length
    : 0;

  return {
    totalStudyTime,
    pomodoroSessions,
    stopwatchSessions,
    completedSessions,
    averageSessionLength,
  };
};

/**
 * Generates a session summary
 */
export const generateSessionSummary = (session: {
  duration: number;
  mode: TimerMode;
  phase: TimerPhase;
  subject: string;
  taskName?: string;
  productivityRating?: number;
}): string => {
  const duration = formatDuration(session.duration);
  const mode = session.mode === 'pomodoro' ? 'Pomodoro' : 'Stopwatch';
  const subject = session.subject;
  const task = session.taskName ? ` - ${session.taskName}` : '';
  const rating = session.productivityRating 
    ? ` (${session.productivityRating}/5 stars)` 
    : '';

  return `${mode} session: ${duration} on ${subject}${task}${rating}`;
};

/**
 * Calculates streak information
 */
export const calculateStreak = (sessions: Array<{
  startTime: Date;
  completed: boolean;
  phase: TimerPhase;
  mode: TimerMode;
}>): {
  currentStreak: number;
  longestStreak: number;
  lastStudyDate: Date | null;
} => {
  const studySessions = sessions
    .filter(session => 
      session.completed && 
      (session.phase === 'work' || session.mode === 'stopwatch')
    )
    .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());

  if (studySessions.length === 0) {
    return { currentStreak: 0, longestStreak: 0, lastStudyDate: null };
  }

  const dates = Array.from(new Set(
    studySessions.map(session => {
      const date = new Date(session.startTime);
      date.setHours(0, 0, 0, 0);
      return date.getTime();
    })
  )).sort((a, b) => b - a);

  let currentStreak = 0;
  let longestStreak = 0;
  let tempStreak = 0;

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Calculate current streak
  for (let i = 0; i < dates.length; i++) {
    const currentDate = new Date(dates[i]);
    const expectedDate = new Date(today);
    expectedDate.setDate(expectedDate.getDate() - i);

    if (currentDate.getTime() === expectedDate.getTime()) {
      currentStreak++;
    } else if (i === 0 && currentDate.getTime() === yesterday.getTime()) {
      // If first date is yesterday, start counting from yesterday
      currentStreak++;
    } else {
      break;
    }
  }

  // Calculate longest streak
  for (let i = 0; i < dates.length; i++) {
    if (i === 0) {
      tempStreak = 1;
    } else {
      const currentDate = new Date(dates[i]);
      const previousDate = new Date(dates[i - 1]);
      const dayDifference = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);

      if (dayDifference === 1) {
        tempStreak++;
      } else {
        longestStreak = Math.max(longestStreak, tempStreak);
        tempStreak = 1;
      }
    }
  }
  longestStreak = Math.max(longestStreak, tempStreak);

  return {
    currentStreak,
    longestStreak,
    lastStudyDate: studySessions.length > 0 ? new Date(studySessions[0].startTime) : null,
  };
};
